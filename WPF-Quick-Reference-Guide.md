# WPF AI开发快速参考指南

## 🚀 快速开始检查清单

### 必备组件
- [ ] .NET 8.0 或更高版本
- [ ] Syncfusion WPF组件库
- [ ] Windows 11主题包
- [ ] Microsoft.Extensions.DependencyInjection

### 项目初始化
```bash
# 1. 创建WPF项目
dotnet new wpf -n YourModuleName

# 2. 添加Syncfusion包
dotnet add package Syncfusion.SfGrid.WPF
dotnet add package Syncfusion.Themes.Windows11Light.WPF

# 3. 添加依赖注入
dotnet add package Microsoft.Extensions.DependencyInjection
```

## 📁 标准项目结构

```
YourModule/
├── Views/           # XAML界面文件
├── ViewModels/      # 视图模型
├── Models/          # 数据模型
├── Services/        # 业务服务
├── Styles/          # 样式和主题
├── Templates/       # 数据和控件模板
├── Converters/      # 值转换器
├── Tests/           # 测试文件
└── Resources/       # 资源文件
```

## 🎯 MVVM核心模板

### BaseViewModel
```csharp
public abstract class BaseViewModel : INotifyPropertyChanged
{
    public event PropertyChangedEventHandler PropertyChanged;
    
    protected bool SetProperty<T>(ref T backingStore, T value, [CallerMemberName] string propertyName = "")
    {
        if (EqualityComparer<T>.Default.Equals(backingStore, value))
            return false;
        backingStore = value;
        OnPropertyChanged(propertyName);
        return true;
    }
    
    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
```

### RelayCommand
```csharp
public class RelayCommand : ICommand
{
    private readonly Action<object> _execute;
    private readonly Func<object, bool> _canExecute;
    
    public RelayCommand(Action<object> execute, Func<object, bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }
    
    public event EventHandler CanExecuteChanged
    {
        add { CommandManager.RequerySuggested += value; }
        remove { CommandManager.RequerySuggested -= value; }
    }
    
    public bool CanExecute(object parameter) => _canExecute?.Invoke(parameter) ?? true;
    public void Execute(object parameter) => _execute(parameter);
}
```

## 🎨 Syncfusion组件快速配置

### App.xaml.cs主题设置
```csharp
protected override void OnStartup(StartupEventArgs e)
{
    SfSkinManager.ApplyStylesOnApplication = true;
    SfSkinManager.SetTheme(this, new Theme("Windows11Light"));
    base.OnStartup(e);
}
```

### 常用组件示例
```xml
<!-- 数据网格 -->
<syncfusion:SfDataGrid ItemsSource="{Binding Items}"
                       SelectedItem="{Binding SelectedItem}"
                       AutoGenerateColumns="False"/>

<!-- 输入框 -->
<syncfusion:SfTextInputLayout Hint="请输入...">
    <TextBox Text="{Binding InputText}"/>
</syncfusion:SfTextInputLayout>

<!-- 图表 -->
<syncfusion:SfChart>
    <syncfusion:ColumnSeries ItemsSource="{Binding Data}"
                            XBindingPath="Category"
                            YBindingPath="Value"/>
</syncfusion:SfChart>
```

## 🎭 Windows 11样式要点

### 核心颜色
```xml
<Color x:Key="SystemAccentColor">#FF0078D4</Color>
<CornerRadius x:Key="ControlCornerRadius">4</CornerRadius>
<DropShadowEffect x:Key="CardShadow" BlurRadius="8" ShadowDepth="2"/>
```

### 按钮样式
```xml
<Style x:Key="Windows11Button" TargetType="Button">
    <Setter Property="Background" Value="{StaticResource SystemAccentBrush}"/>
    <Setter Property="CornerRadius" Value="4"/>
    <Setter Property="Padding" Value="16,8"/>
</Style>
```

## 🔄 数据绑定最佳实践

### 属性绑定
```xml
<!-- 双向绑定 -->
<TextBox Text="{Binding UserName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>

<!-- 单向绑定 -->
<TextBlock Text="{Binding DisplayText, Mode=OneWay}"/>

<!-- 命令绑定 -->
<Button Command="{Binding SaveCommand}" CommandParameter="{Binding SelectedItem}"/>
```

### 集合绑定
```csharp
public ObservableCollection<ItemModel> Items { get; set; }
public ICollectionView ItemsView { get; private set; }

// 在构造函数中
ItemsView = CollectionViewSource.GetDefaultView(Items);
ItemsView.Filter = FilterItems;
```

## ⚡ 性能优化要点

### UI虚拟化
```xml
<ListBox VirtualizingPanel.IsVirtualizing="True"
         VirtualizingPanel.VirtualizationMode="Recycling"/>
```

### 异步操作
```csharp
public async Task LoadDataAsync()
{
    IsLoading = true;
    try
    {
        var data = await _service.GetDataAsync().ConfigureAwait(false);
        await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            Items.Clear();
            foreach (var item in data)
                Items.Add(item);
        });
    }
    finally
    {
        IsLoading = false;
    }
}
```

### 内存管理
```csharp
public class ViewModel : BaseViewModel, IDisposable
{
    public void Dispose()
    {
        // 清理事件订阅
        WeakEventManager<Service, EventArgs>.RemoveHandler(_service, "Event", OnEvent);
        // 清理其他资源
    }
}
```

## 🧪 测试模板

### 单元测试
```csharp
[TestClass]
public class ViewModelTests
{
    private ViewModel _viewModel;
    private Mock<IService> _mockService;
    
    [TestInitialize]
    public void Setup()
    {
        _mockService = new Mock<IService>();
        _viewModel = new ViewModel(_mockService.Object);
    }
    
    [TestMethod]
    public void Command_ShouldExecute_WhenConditionMet()
    {
        // Arrange
        _mockService.Setup(s => s.GetData()).Returns(expectedData);
        
        // Act
        _viewModel.LoadCommand.Execute(null);
        
        // Assert
        Assert.AreEqual(expectedValue, _viewModel.Property);
        _mockService.Verify(s => s.GetData(), Times.Once);
    }
}
```

## 🚨 常见问题快速解决

| 问题 | 解决方案 |
|------|----------|
| 数据绑定不更新 | 确保实现INotifyPropertyChanged |
| 内存泄漏 | 使用WeakEventManager，实现IDisposable |
| UI卡顿 | 启用虚拟化，使用异步操作 |
| 主题不生效 | 检查Syncfusion主题设置和资源引用 |

## 📋 开发完成检查

### 功能完整性
- [ ] 完整MVVM架构
- [ ] Syncfusion组件使用
- [ ] Windows 11设计语言
- [ ] 独立编译运行

### 代码质量
- [ ] 单元测试覆盖
- [ ] 异步编程模式
- [ ] 错误处理机制
- [ ] 性能优化措施

## 🔗 相关文档

- [完整开发规则](./WPF-AI-Development-Guidelines.md)
- [项目结构模板](./WPF-Module-Template-Structure.md)
- [Syncfusion官方文档](https://help.syncfusion.com/wpf/welcome-to-syncfusion-essential-wpf)
- [Windows 11设计指南](https://docs.microsoft.com/en-us/windows/apps/design/)

---

💡 **提示**: 始终遵循这些标准，确保代码的一致性和可维护性！
