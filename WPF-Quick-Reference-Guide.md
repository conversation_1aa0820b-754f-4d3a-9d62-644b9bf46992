# WPF AI开发快速参考指南

## 🎯 使用说明

**本指南专门用于在现有WPF项目中开发单个功能模块**

- **目标**: 为现有项目添加新功能模块，而非创建新项目
- **方式**: AI生成可直接集成的完整代码
- **要求**: 生成的代码必须即插即用，无需修改即可运行

## 🚀 快速开始检查清单

### 现有项目要求
- [ ] 现有WPF项目（.NET Framework 4.8+ 或 .NET 6+）
- [ ] 项目支持NuGet包管理
- [ ] 具备基本的MVVM架构（推荐但非必需）

### 必需依赖包
```xml
<!-- 在现有项目的.csproj中添加以下包 -->
<PackageReference Include="Syncfusion.SfGrid.WPF" Version="23.2.7" />
<PackageReference Include="Syncfusion.SfChart.WPF" Version="23.2.7" />
<PackageReference Include="Syncfusion.SfInput.WPF" Version="23.2.7" />
<PackageReference Include="Syncfusion.Themes.Windows11Light.WPF" Version="23.2.7" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
```

### 快速安装命令
```bash
# 在现有项目目录中执行
dotnet add package Syncfusion.SfGrid.WPF
dotnet add package Syncfusion.Themes.Windows11Light.WPF
dotnet add package Microsoft.Extensions.DependencyInjection
```

## 📁 模块文件组织

### 推荐的文件结构
```
现有项目/
├── Views/
│   └── [ModuleName]/        # 新模块视图
│       ├── [Module]View.xaml
│       └── [Module]View.xaml.cs
├── ViewModels/
│   └── [Module]ViewModel.cs # 新模块ViewModel
├── Models/
│   └── [ModuleName]/        # 新模块数据模型
├── Services/
│   └── [ModuleName]/        # 新模块服务
├── Styles/
│   └── [ModuleName]/        # 新模块样式
└── Resources/
    └── [ModuleName]/        # 新模块资源
```

### 文件命名规范
- **Views**: `{功能名}View.xaml` (例: `UserManagementView.xaml`)
- **ViewModels**: `{功能名}ViewModel.cs` (例: `UserManagementViewModel.cs`)
- **Models**: `{实体名}Model.cs` (例: `UserModel.cs`)
- **Services**: `I{服务名}Service.cs`, `{服务名}Service.cs`

## 🎯 MVVM核心模板

### BaseViewModel
```csharp
public abstract class BaseViewModel : INotifyPropertyChanged
{
    public event PropertyChangedEventHandler PropertyChanged;
    
    protected bool SetProperty<T>(ref T backingStore, T value, [CallerMemberName] string propertyName = "")
    {
        if (EqualityComparer<T>.Default.Equals(backingStore, value))
            return false;
        backingStore = value;
        OnPropertyChanged(propertyName);
        return true;
    }
    
    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
```

### RelayCommand
```csharp
public class RelayCommand : ICommand
{
    private readonly Action<object> _execute;
    private readonly Func<object, bool> _canExecute;
    
    public RelayCommand(Action<object> execute, Func<object, bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }
    
    public event EventHandler CanExecuteChanged
    {
        add { CommandManager.RequerySuggested += value; }
        remove { CommandManager.RequerySuggested -= value; }
    }
    
    public bool CanExecute(object parameter) => _canExecute?.Invoke(parameter) ?? true;
    public void Execute(object parameter) => _execute(parameter);
}
```

## 🎨 Syncfusion组件快速配置

### App.xaml.cs主题设置
```csharp
protected override void OnStartup(StartupEventArgs e)
{
    SfSkinManager.ApplyStylesOnApplication = true;
    SfSkinManager.SetTheme(this, new Theme("Windows11Light"));
    base.OnStartup(e);
}
```

### 常用组件示例
```xml
<!-- 数据网格 -->
<syncfusion:SfDataGrid ItemsSource="{Binding Items}"
                       SelectedItem="{Binding SelectedItem}"
                       AutoGenerateColumns="False"/>

<!-- 输入框 -->
<syncfusion:SfTextInputLayout Hint="请输入...">
    <TextBox Text="{Binding InputText}"/>
</syncfusion:SfTextInputLayout>

<!-- 图表 -->
<syncfusion:SfChart>
    <syncfusion:ColumnSeries ItemsSource="{Binding Data}"
                            XBindingPath="Category"
                            YBindingPath="Value"/>
</syncfusion:SfChart>
```

## 🎭 Windows 11样式要点

### 核心颜色
```xml
<Color x:Key="SystemAccentColor">#FF0078D4</Color>
<CornerRadius x:Key="ControlCornerRadius">4</CornerRadius>
<DropShadowEffect x:Key="CardShadow" BlurRadius="8" ShadowDepth="2"/>
```

### 按钮样式
```xml
<Style x:Key="Windows11Button" TargetType="Button">
    <Setter Property="Background" Value="{StaticResource SystemAccentBrush}"/>
    <Setter Property="CornerRadius" Value="4"/>
    <Setter Property="Padding" Value="16,8"/>
</Style>
```

## 🔄 数据绑定最佳实践

### 属性绑定
```xml
<!-- 双向绑定 -->
<TextBox Text="{Binding UserName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>

<!-- 单向绑定 -->
<TextBlock Text="{Binding DisplayText, Mode=OneWay}"/>

<!-- 命令绑定 -->
<Button Command="{Binding SaveCommand}" CommandParameter="{Binding SelectedItem}"/>
```

### 集合绑定
```csharp
public ObservableCollection<ItemModel> Items { get; set; }
public ICollectionView ItemsView { get; private set; }

// 在构造函数中
ItemsView = CollectionViewSource.GetDefaultView(Items);
ItemsView.Filter = FilterItems;
```

## ⚡ 性能优化要点

### UI虚拟化
```xml
<ListBox VirtualizingPanel.IsVirtualizing="True"
         VirtualizingPanel.VirtualizationMode="Recycling"/>
```

### 异步操作
```csharp
public async Task LoadDataAsync()
{
    IsLoading = true;
    try
    {
        var data = await _service.GetDataAsync().ConfigureAwait(false);
        await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            Items.Clear();
            foreach (var item in data)
                Items.Add(item);
        });
    }
    finally
    {
        IsLoading = false;
    }
}
```

### 内存管理
```csharp
public class ViewModel : BaseViewModel, IDisposable
{
    public void Dispose()
    {
        // 清理事件订阅
        WeakEventManager<Service, EventArgs>.RemoveHandler(_service, "Event", OnEvent);
        // 清理其他资源
    }
}
```

## 🧪 测试模板

### 单元测试
```csharp
[TestClass]
public class ViewModelTests
{
    private ViewModel _viewModel;
    private Mock<IService> _mockService;
    
    [TestInitialize]
    public void Setup()
    {
        _mockService = new Mock<IService>();
        _viewModel = new ViewModel(_mockService.Object);
    }
    
    [TestMethod]
    public void Command_ShouldExecute_WhenConditionMet()
    {
        // Arrange
        _mockService.Setup(s => s.GetData()).Returns(expectedData);
        
        // Act
        _viewModel.LoadCommand.Execute(null);
        
        // Assert
        Assert.AreEqual(expectedValue, _viewModel.Property);
        _mockService.Verify(s => s.GetData(), Times.Once);
    }
}
```

## 🔧 快速集成步骤

### 1. 添加生成的文件到项目
```bash
# 将AI生成的文件复制到对应位置
Views/[ModuleName]/[ModuleName]View.xaml
Views/[ModuleName]/[ModuleName]View.xaml.cs
ViewModels/[ModuleName]ViewModel.cs
Models/[ModuleName]/[Entity]Model.cs
Styles/[ModuleName]/[ModuleName]Styles.xaml
```

### 2. 更新App.xaml资源引用
```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <!-- 添加新模块样式 -->
            <ResourceDictionary Source="Styles/[ModuleName]/[ModuleName]Styles.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

### 3. 在主界面中使用模块
```xml
<!-- 方式1: 直接在XAML中使用 -->
<TabItem Header="新功能">
    <local:NewModuleView/>
</TabItem>

<!-- 方式2: 通过导航使用 -->
<ContentPresenter Content="{Binding CurrentView}"/>
```

### 4. 配置ViewModel（如使用依赖注入）
```csharp
// 在App.xaml.cs或Startup中
services.AddTransient<NewModuleViewModel>();
```

## 🚨 常见问题快速解决

| 问题 | 解决方案 |
|------|----------|
| 编译错误：找不到类型 | 检查命名空间引用和using语句 |
| 样式不生效 | 确保在App.xaml中正确引用样式文件 |
| 数据绑定不更新 | 确保实现INotifyPropertyChanged |
| Syncfusion控件显示异常 | 检查NuGet包版本和主题设置 |
| 内存泄漏 | 使用WeakEventManager，实现IDisposable |
| UI卡顿 | 启用虚拟化，使用异步操作 |

## 📋 集成验证检查

### 基本集成
- [ ] 所有文件已添加到项目中
- [ ] 项目可以成功编译
- [ ] 没有编译错误或警告
- [ ] 模块可以正常显示

### 功能验证
- [ ] 数据绑定正常工作
- [ ] 命令可以正确执行
- [ ] 样式显示正确
- [ ] 动画效果正常

### 性能检查
- [ ] 没有内存泄漏
- [ ] UI响应流畅
- [ ] 异步操作正常

## 📋 AI代码生成检查

### 代码完整性
- [ ] 包含完整文件内容（非片段）
- [ ] 所有using语句完整
- [ ] 命名空间声明正确
- [ ] 文件放置位置明确

### 依赖说明
- [ ] 列出所有必需NuGet包
- [ ] 说明版本要求
- [ ] 提供安装命令
- [ ] 集成步骤清晰

## 🔗 相关文档

- [完整开发规则](./WPF-AI-Development-Guidelines.md)
- [项目结构模板](./WPF-Module-Template-Structure.md)
- [Syncfusion官方文档](https://help.syncfusion.com/wpf/welcome-to-syncfusion-essential-wpf)
- [Windows 11设计指南](https://docs.microsoft.com/en-us/windows/apps/design/)

---

💡 **重要提示**:
- 本指南专门用于现有项目中的模块开发
- AI生成的代码应该可以直接使用，无需修改
- 遇到问题时，首先检查依赖包和资源引用
