# WPF功能模块标准项目结构模板

## 完整项目结构示例

```
UserManagementModule/
├── UserManagementModule.csproj
├── App.xaml
├── App.xaml.cs
├── Views/
│   ├── MainView.xaml
│   ├── MainView.xaml.cs
│   ├── UserDetailView.xaml
│   ├── UserDetailView.xaml.cs
│   └── UserControls/
│       ├── UserCard.xaml
│       ├── UserCard.xaml.cs
│       ├── SearchBox.xaml
│       └── SearchBox.xaml.cs
├── ViewModels/
│   ├── BaseViewModel.cs
│   ├── MainViewModel.cs
│   ├── UserDetailViewModel.cs
│   └── Commands/
│       ├── RelayCommand.cs
│       └── AsyncRelayCommand.cs
├── Models/
│   ├── DataModels/
│   │   ├── User.cs
│   │   ├── UserRole.cs
│   │   └── UserPermission.cs
│   └── BusinessModels/
│       ├── UserSearchCriteria.cs
│       └── UserStatistics.cs
├── Services/
│   ├── Interfaces/
│   │   ├── IUserService.cs
│   │   ├── IAuthenticationService.cs
│   │   └── ILoggingService.cs
│   └── Implementations/
│       ├── UserService.cs
│       ├── AuthenticationService.cs
│       └── LoggingService.cs
├── Styles/
│   ├── Themes/
│   │   ├── Windows11Light.xaml
│   │   ├── Windows11Dark.xaml
│   │   └── Generic.xaml
│   ├── Controls/
│   │   ├── ButtonStyles.xaml
│   │   ├── TextBoxStyles.xaml
│   │   └── DataGridStyles.xaml
│   └── Resources/
│       ├── Colors.xaml
│       ├── Brushes.xaml
│       └── Effects.xaml
├── Templates/
│   ├── DataTemplates/
│   │   ├── UserDataTemplate.xaml
│   │   └── RoleDataTemplate.xaml
│   └── ControlTemplates/
│       ├── CustomButtonTemplate.xaml
│       └── CustomTextBoxTemplate.xaml
├── Converters/
│   ├── BoolToVisibilityConverter.cs
│   ├── DateTimeToStringConverter.cs
│   └── StatusToColorConverter.cs
├── Behaviors/
│   ├── TextBoxBehaviors.cs
│   ├── DataGridBehaviors.cs
│   └── WindowBehaviors.cs
├── Tests/
│   ├── UnitTests/
│   │   ├── ViewModelTests/
│   │   │   ├── MainViewModelTests.cs
│   │   │   └── UserDetailViewModelTests.cs
│   │   ├── ServiceTests/
│   │   │   ├── UserServiceTests.cs
│   │   │   └── AuthenticationServiceTests.cs
│   │   └── ConverterTests/
│   │       └── BoolToVisibilityConverterTests.cs
│   └── IntegrationTests/
│       ├── ViewIntegrationTests.cs
│       └── ServiceIntegrationTests.cs
├── Resources/
│   ├── Images/
│   │   ├── Icons/
│   │   │   ├── user.png
│   │   │   ├── settings.png
│   │   │   └── logout.png
│   │   └── Backgrounds/
│   │       └── default-bg.jpg
│   └── Localization/
│       ├── Resources.resx
│       ├── Resources.zh-CN.resx
│       └── Resources.en-US.resx
├── Configuration/
│   ├── AppSettings.json
│   └── DependencyInjection.cs
└── Documentation/
    ├── README.md
    ├── API-Documentation.md
    └── User-Guide.md
```

## 核心文件模板

### 1. 项目文件 (UserManagementModule.csproj)
```xml
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <AssemblyTitle>用户管理模块</AssemblyTitle>
    <AssemblyDescription>完整的用户管理功能模块</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Syncfusion.SfGrid.WPF" Version="23.2.7" />
    <PackageReference Include="Syncfusion.SfChart.WPF" Version="23.2.7" />
    <PackageReference Include="Syncfusion.SfInput.WPF" Version="23.2.7" />
    <PackageReference Include="Syncfusion.Themes.Windows11Light.WPF" Version="23.2.7" />
    <PackageReference Include="Syncfusion.Themes.Windows11Dark.WPF" Version="23.2.7" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="MSTest.TestFramework" Version="3.1.1" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.1.1" />
    <PackageReference Include="Moq" Version="4.20.69" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\Images\**\*" />
    <EmbeddedResource Include="Resources\Localization\*.resx" />
  </ItemGroup>

</Project>
```

### 2. 应用程序入口 (App.xaml)
```xml
<Application x:Class="UserManagementModule.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
             StartupUri="Views/MainView.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Syncfusion主题 -->
                <syncfusion:Windows11LightThemeSettings x:Key="Windows11Light" />
                <syncfusion:Windows11DarkThemeSettings x:Key="Windows11Dark" />
                
                <!-- 自定义样式 -->
                <ResourceDictionary Source="Styles/Resources/Colors.xaml"/>
                <ResourceDictionary Source="Styles/Resources/Brushes.xaml"/>
                <ResourceDictionary Source="Styles/Resources/Effects.xaml"/>
                <ResourceDictionary Source="Styles/Controls/ButtonStyles.xaml"/>
                <ResourceDictionary Source="Styles/Controls/TextBoxStyles.xaml"/>
                <ResourceDictionary Source="Styles/Controls/DataGridStyles.xaml"/>
                
                <!-- 数据模板 -->
                <ResourceDictionary Source="Templates/DataTemplates/UserDataTemplate.xaml"/>
                <ResourceDictionary Source="Templates/DataTemplates/RoleDataTemplate.xaml"/>
                
                <!-- 控件模板 -->
                <ResourceDictionary Source="Templates/ControlTemplates/CustomButtonTemplate.xaml"/>
                <ResourceDictionary Source="Templates/ControlTemplates/CustomTextBoxTemplate.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
```

### 3. 应用程序代码后台 (App.xaml.cs)
```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Syncfusion.SfSkinManager;
using System.Windows;
using UserManagementModule.Services.Interfaces;
using UserManagementModule.Services.Implementations;
using UserManagementModule.ViewModels;

namespace UserManagementModule
{
    public partial class App : Application
    {
        private ServiceProvider _serviceProvider;
        
        protected override void OnStartup(StartupEventArgs e)
        {
            // 配置Syncfusion主题
            SfSkinManager.ApplyStylesOnApplication = true;
            SfSkinManager.SetTheme(this, new Theme("Windows11Light"));
            
            // 配置依赖注入
            ConfigureServices();
            
            base.OnStartup(e);
        }
        
        private void ConfigureServices()
        {
            var services = new ServiceCollection();
            
            // 配置
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("Configuration/AppSettings.json", optional: false)
                .Build();
            services.AddSingleton<IConfiguration>(configuration);
            
            // 日志
            services.AddLogging(builder => builder.AddConsole());
            
            // 服务
            services.AddTransient<IUserService, UserService>();
            services.AddTransient<IAuthenticationService, AuthenticationService>();
            services.AddTransient<ILoggingService, LoggingService>();
            
            // ViewModels
            services.AddTransient<MainViewModel>();
            services.AddTransient<UserDetailViewModel>();
            
            _serviceProvider = services.BuildServiceProvider();
        }
        
        protected override void OnExit(ExitEventArgs e)
        {
            _serviceProvider?.Dispose();
            base.OnExit(e);
        }
    }
}
```

### 4. 主视图 (Views/MainView.xaml)
```xml
<Window x:Class="UserManagementModule.Views.MainView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
        xmlns:local="clr-namespace:UserManagementModule.ViewModels"
        Title="用户管理系统" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Style="{StaticResource Windows11WindowStyle}">
    
    <Window.DataContext>
        <local:MainViewModel/>
    </Window.DataContext>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Style="{StaticResource TitleBarStyle}">
            <StackPanel Orientation="Horizontal" Margin="20,10">
                <TextBlock Text="用户管理系统" 
                          Style="{StaticResource TitleTextStyle}"/>
                <Button Content="设置" 
                       Style="{StaticResource IconButtonStyle}"
                       Command="{Binding SettingsCommand}"
                       Margin="20,0,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧面板 -->
            <Border Grid.Column="0" Style="{StaticResource SidePanelStyle}">
                <StackPanel>
                    <!-- 搜索框 -->
                    <syncfusion:SfTextInputLayout Hint="搜索用户..."
                                                 Margin="10">
                        <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>
                    </syncfusion:SfTextInputLayout>
                    
                    <!-- 用户列表 -->
                    <ListBox ItemsSource="{Binding Users}"
                            SelectedItem="{Binding SelectedUser}"
                            ItemTemplate="{StaticResource UserDataTemplate}"
                            Style="{StaticResource UserListStyle}"/>
                </StackPanel>
            </Border>
            
            <!-- 右侧详情面板 -->
            <Border Grid.Column="1" Style="{StaticResource DetailPanelStyle}" Margin="10,0,0,0">
                <ContentPresenter Content="{Binding SelectedUser}"
                                ContentTemplate="{StaticResource UserDetailTemplate}"/>
            </Border>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <ProgressBar Value="{Binding ProgressValue}" 
                           Width="200" Height="20"
                           Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
```

## 使用说明

1. **创建新模块**: 复制此模板结构，根据具体功能需求修改命名空间和类名
2. **配置依赖**: 在App.xaml.cs中配置所需的服务和依赖注入
3. **实现功能**: 按照MVVM模式实现具体的业务逻辑
4. **添加测试**: 为每个组件编写相应的单元测试和集成测试
5. **样式定制**: 根据Windows 11设计语言定制控件样式和主题

此模板确保每个功能模块都具有完整的结构和标准化的实现方式。
