# WPF模块集成示例

## 📋 场景说明

假设您有一个现有的WPF项目，现在需要添加一个"用户管理"功能模块。以下是完整的集成示例。

## 🎯 生成的模块文件

### 1. 用户管理视图 (Views/UserManagement/UserManagementView.xaml)

```xml
<UserControl x:Class="YourProject.Views.UserManagement.UserManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
             xmlns:local="clr-namespace:YourProject.ViewModels">
    
    <UserControl.DataContext>
        <local:UserManagementViewModel/>
    </UserControl.DataContext>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题和搜索 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="用户管理" FontSize="24" FontWeight="Bold" VerticalAlignment="Center"/>
            <syncfusion:SfTextInputLayout Hint="搜索用户..." Width="300" Margin="20,0,0,0">
                <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>
            </syncfusion:SfTextInputLayout>
        </StackPanel>
        
        <!-- 用户列表 -->
        <syncfusion:SfDataGrid Grid.Row="1" 
                               ItemsSource="{Binding Users}"
                               SelectedItem="{Binding SelectedUser}"
                               AutoGenerateColumns="False"
                               AllowEditing="True"
                               AllowSorting="True"
                               AllowFiltering="True">
            <syncfusion:SfDataGrid.Columns>
                <syncfusion:GridTextColumn MappingName="Name" HeaderText="姓名" Width="200"/>
                <syncfusion:GridTextColumn MappingName="Email" HeaderText="邮箱" Width="250"/>
                <syncfusion:GridTextColumn MappingName="Department" HeaderText="部门" Width="150"/>
                <syncfusion:GridDateTimeColumn MappingName="CreatedDate" HeaderText="创建日期" Width="150"/>
            </syncfusion:SfDataGrid.Columns>
        </syncfusion:SfDataGrid>
        
        <!-- 操作按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="新增用户" Command="{Binding AddUserCommand}" 
                    Style="{StaticResource Windows11ButtonStyle}" Margin="0,0,10,0"/>
            <Button Content="编辑用户" Command="{Binding EditUserCommand}" 
                    Style="{StaticResource Windows11ButtonStyle}" Margin="0,0,10,0"/>
            <Button Content="删除用户" Command="{Binding DeleteUserCommand}" 
                    Style="{StaticResource Windows11ButtonStyle}"/>
        </StackPanel>
    </Grid>
</UserControl>
```

### 2. 用户管理视图代码后台 (Views/UserManagement/UserManagementView.xaml.cs)

```csharp
/*
 * 文件名: UserManagementView.xaml.cs
 * 功能: 用户管理界面代码后台
 * 创建: AI Assistant
 * 说明: 用户管理模块的主视图，提供用户列表显示和基本操作功能
 */

using System.Windows.Controls;

namespace YourProject.Views.UserManagement
{
    /// <summary>
    /// 用户管理视图
    /// </summary>
    public partial class UserManagementView : UserControl
    {
        public UserManagementView()
        {
            InitializeComponent();
        }
    }
}
```

### 3. 用户管理视图模型 (ViewModels/UserManagementViewModel.cs)

```csharp
/*
 * 文件名: UserManagementViewModel.cs
 * 功能: 用户管理视图模型
 * 创建: AI Assistant
 * 说明: 实现用户管理的业务逻辑和数据绑定
 */

using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using YourProject.Models.UserManagement;

namespace YourProject.ViewModels
{
    /// <summary>
    /// 用户管理视图模型
    /// </summary>
    public class UserManagementViewModel : INotifyPropertyChanged
    {
        private string _searchText;
        private UserModel _selectedUser;
        private ObservableCollection<UserModel> _users;
        
        public event PropertyChangedEventHandler PropertyChanged;
        
        /// <summary>
        /// 搜索文本
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    FilterUsers();
                }
            }
        }
        
        /// <summary>
        /// 选中的用户
        /// </summary>
        public UserModel SelectedUser
        {
            get => _selectedUser;
            set => SetProperty(ref _selectedUser, value);
        }
        
        /// <summary>
        /// 用户列表
        /// </summary>
        public ObservableCollection<UserModel> Users
        {
            get => _users;
            set => SetProperty(ref _users, value);
        }
        
        /// <summary>
        /// 添加用户命令
        /// </summary>
        public ICommand AddUserCommand { get; }
        
        /// <summary>
        /// 编辑用户命令
        /// </summary>
        public ICommand EditUserCommand { get; }
        
        /// <summary>
        /// 删除用户命令
        /// </summary>
        public ICommand DeleteUserCommand { get; }
        
        public UserManagementViewModel()
        {
            Users = new ObservableCollection<UserModel>();
            
            AddUserCommand = new RelayCommand(AddUser);
            EditUserCommand = new RelayCommand(EditUser, CanEditUser);
            DeleteUserCommand = new RelayCommand(DeleteUser, CanDeleteUser);
            
            LoadSampleData();
        }
        
        private void AddUser(object parameter)
        {
            // 添加用户逻辑
            var newUser = new UserModel
            {
                Id = Users.Count + 1,
                Name = "新用户",
                Email = "<EMAIL>",
                Department = "IT部门",
                CreatedDate = DateTime.Now
            };
            Users.Add(newUser);
        }
        
        private void EditUser(object parameter)
        {
            // 编辑用户逻辑
            if (SelectedUser != null)
            {
                // 这里可以打开编辑对话框或导航到编辑页面
            }
        }
        
        private bool CanEditUser(object parameter)
        {
            return SelectedUser != null;
        }
        
        private void DeleteUser(object parameter)
        {
            // 删除用户逻辑
            if (SelectedUser != null)
            {
                Users.Remove(SelectedUser);
                SelectedUser = null;
            }
        }
        
        private bool CanDeleteUser(object parameter)
        {
            return SelectedUser != null;
        }
        
        private void FilterUsers()
        {
            // 实现搜索过滤逻辑
            // 这里可以使用CollectionView进行过滤
        }
        
        private void LoadSampleData()
        {
            // 加载示例数据
            Users.Add(new UserModel { Id = 1, Name = "张三", Email = "<EMAIL>", Department = "开发部", CreatedDate = DateTime.Now.AddDays(-30) });
            Users.Add(new UserModel { Id = 2, Name = "李四", Email = "<EMAIL>", Department = "测试部", CreatedDate = DateTime.Now.AddDays(-20) });
            Users.Add(new UserModel { Id = 3, Name = "王五", Email = "<EMAIL>", Department = "产品部", CreatedDate = DateTime.Now.AddDays(-10) });
        }
        
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        
        protected bool SetProperty<T>(ref T backingStore, T value, [CallerMemberName] string propertyName = "")
        {
            if (Equals(backingStore, value))
                return false;
                
            backingStore = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
    
    /// <summary>
    /// 简单的命令实现
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action<object> _execute;
        private readonly Func<object, bool> _canExecute;
        
        public RelayCommand(Action<object> execute, Func<object, bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }
        
        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }
        
        public bool CanExecute(object parameter) => _canExecute?.Invoke(parameter) ?? true;
        public void Execute(object parameter) => _execute(parameter);
    }
}
```

### 4. 用户数据模型 (Models/UserManagement/UserModel.cs)

```csharp
/*
 * 文件名: UserModel.cs
 * 功能: 用户数据模型
 * 创建: AI Assistant
 * 说明: 定义用户实体的数据结构
 */

using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace YourProject.Models.UserManagement
{
    /// <summary>
    /// 用户数据模型
    /// </summary>
    public class UserModel : INotifyPropertyChanged
    {
        private int _id;
        private string _name;
        private string _email;
        private string _department;
        private DateTime _createdDate;
        
        public event PropertyChangedEventHandler PropertyChanged;
        
        /// <summary>
        /// 用户ID
        /// </summary>
        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }
        
        /// <summary>
        /// 用户姓名
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }
        
        /// <summary>
        /// 用户邮箱
        /// </summary>
        public string Email
        {
            get => _email;
            set => SetProperty(ref _email, value);
        }
        
        /// <summary>
        /// 所属部门
        /// </summary>
        public string Department
        {
            get => _department;
            set => SetProperty(ref _department, value);
        }
        
        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime CreatedDate
        {
            get => _createdDate;
            set => SetProperty(ref _createdDate, value);
        }
        
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        
        protected bool SetProperty<T>(ref T backingStore, T value, [CallerMemberName] string propertyName = "")
        {
            if (Equals(backingStore, value))
                return false;
                
            backingStore = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
```

### 5. 模块样式文件 (Styles/UserManagement/UserManagementStyles.xaml)

```xml
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Windows 11按钮样式 -->
    <Style x:Key="Windows11ButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#FF0078D4"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#FF0078D4"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#FF40E0FF"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#FF0063B1"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="#FFE6E6E6"/>
                            <Setter Property="Foreground" Value="#FFCCCCCC"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
```

## 🔧 集成步骤

### 步骤1: 安装必需的NuGet包

在现有项目中执行以下命令：

```bash
dotnet add package Syncfusion.SfGrid.WPF --version 23.2.7
dotnet add package Syncfusion.SfInput.WPF --version 23.2.7
dotnet add package Syncfusion.Themes.Windows11Light.WPF --version 23.2.7
```

### 步骤2: 添加文件到项目

将上述生成的文件按以下结构添加到您的现有项目中：

```
YourProject/
├── Views/
│   └── UserManagement/
│       ├── UserManagementView.xaml          # 新增
│       └── UserManagementView.xaml.cs       # 新增
├── ViewModels/
│   └── UserManagementViewModel.cs           # 新增
├── Models/
│   └── UserManagement/
│       └── UserModel.cs                     # 新增
└── Styles/
    └── UserManagement/
        └── UserManagementStyles.xaml        # 新增
```

### 步骤3: 更新App.xaml

在您的App.xaml文件中添加样式资源引用：

```xml
<Application x:Class="YourProject.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 现有的资源字典 -->

                <!-- 新增：用户管理模块样式 -->
                <ResourceDictionary Source="Styles/UserManagement/UserManagementStyles.xaml"/>

                <!-- Syncfusion主题（如果尚未添加） -->
                <ResourceDictionary Source="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/SfInput/SfTextInputLayout.xaml" />
                <ResourceDictionary Source="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/SfGrid/SfDataGrid.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
```

### 步骤4: 配置Syncfusion主题

在App.xaml.cs中添加主题配置：

```csharp
using System.Windows;
using Syncfusion.SfSkinManager;

namespace YourProject
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            // 配置Syncfusion主题
            SfSkinManager.ApplyStylesOnApplication = true;
            SfSkinManager.SetTheme(this, new Theme("Windows11Light"));

            base.OnStartup(e);
        }
    }
}
```

### 步骤5: 在主界面中使用模块

在您的MainWindow.xaml或其他容器中添加用户管理模块：

```xml
<!-- 方式1: 作为Tab页使用 -->
<TabControl>
    <!-- 现有的Tab项 -->

    <!-- 新增的用户管理Tab -->
    <TabItem Header="用户管理">
        <local:UserManagementView xmlns:local="clr-namespace:YourProject.Views.UserManagement"/>
    </TabItem>
</TabControl>

<!-- 方式2: 作为独立窗口使用 -->
<Button Content="打开用户管理" Click="OpenUserManagement_Click"/>

<!-- 方式3: 在现有布局中使用 -->
<Grid>
    <Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="*"/>
    </Grid.RowDefinitions>

    <TextBlock Grid.Row="0" Text="系统管理" FontSize="20" Margin="10"/>
    <local:UserManagementView Grid.Row="1" xmlns:local="clr-namespace:YourProject.Views.UserManagement"/>
</Grid>
```

## ✅ 验证集成

### 编译验证
1. 构建项目：`dotnet build`
2. 确保没有编译错误
3. 检查所有引用都已正确解析

### 功能验证
1. 运行应用程序
2. 导航到用户管理模块
3. 验证以下功能：
   - 用户列表正常显示
   - 搜索功能工作正常
   - 按钮点击有响应
   - 数据绑定正常工作

### 样式验证
1. 检查按钮样式是否应用Windows 11风格
2. 验证Syncfusion控件主题是否正确
3. 确认整体界面风格一致

## 🚨 常见问题解决

### 问题1: 编译错误 - 找不到类型
**解决方案**: 检查命名空间引用，确保using语句正确

### 问题2: Syncfusion控件显示异常
**解决方案**:
1. 确认NuGet包版本一致
2. 检查App.xaml中的主题资源引用
3. 验证App.xaml.cs中的主题配置

### 问题3: 样式不生效
**解决方案**: 确保在App.xaml中正确引用了UserManagementStyles.xaml

### 问题4: 数据绑定不工作
**解决方案**: 检查DataContext设置和属性名称是否正确

---

## 📋 总结

通过以上步骤，您已经成功将用户管理模块集成到现有的WPF项目中。这个示例展示了：

1. **完整的文件结构**: 包含View、ViewModel、Model和样式文件
2. **即插即用**: 所有代码都是完整的，可以直接使用
3. **标准化实现**: 遵循MVVM模式和Windows 11设计语言
4. **详细的集成指导**: 提供了完整的集成步骤和验证方法

这个模块现在可以在您的现有项目中正常工作，并且可以根据具体需求进行进一步的定制和扩展。
