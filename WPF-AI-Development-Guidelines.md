# WPF AI开发功能模块规则与指导原则

## 目录
1. [概述](#概述)
2. [技术栈规范](#技术栈规范)
3. [项目结构规范](#项目结构规范)
4. [MVVM架构标准](#mvvm架构标准)
5. [Syncfusion组件使用指南](#syncfusion组件使用指南)
6. [Windows 11主题应用](#windows-11主题应用)
7. [自定义控件开发标准](#自定义控件开发标准)
8. [数据绑定规范](#数据绑定规范)
9. [动画效果实现](#动画效果实现)
10. [测试要求](#测试要求)
11. [代码规范](#代码规范)
12. [性能优化](#性能优化)

## 概述

本文档定义了AI助手开发WPF功能模块时必须遵循的完整规则和标准。每个功能模块必须：
- 实现完整的MVVM架构
- 使用Syncfusion WPF组件库
- 遵循Windows 11设计语言
- 具备独立编译、测试和运行能力
- 包含完整的样式、模板和动画效果

## 技术栈规范

### 必需技术栈
- **.NET Framework/Core**: 最新LTS版本
- **WPF**: Windows Presentation Foundation
- **UI组件库**: Syncfusion WPF组件库（优先）
- **设计语言**: Windows 11 Fluent Design
- **架构模式**: MVVM (Model-View-ViewModel)
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **测试框架**: MSTest 或 xUnit

### 可选技术栈
- **自定义控件**: 当Syncfusion无法满足需求时
- **动画库**: Windows.UI.Xaml.Media.Animation
- **数据访问**: Entity Framework Core
- **配置管理**: Microsoft.Extensions.Configuration

## 项目结构规范

### 标准项目结构
```
ModuleName/
├── ModuleName.csproj
├── Views/
│   ├── MainView.xaml
│   ├── MainView.xaml.cs
│   └── UserControls/
├── ViewModels/
│   ├── MainViewModel.cs
│   ├── BaseViewModel.cs
│   └── Commands/
├── Models/
│   ├── DataModels/
│   └── BusinessModels/
├── Services/
│   ├── Interfaces/
│   └── Implementations/
├── Styles/
│   ├── Themes/
│   ├── Controls/
│   └── Resources/
├── Templates/
│   ├── DataTemplates/
│   └── ControlTemplates/
├── Converters/
├── Behaviors/
├── Tests/
│   ├── UnitTests/
│   └── IntegrationTests/
└── Resources/
    ├── Images/
    ├── Icons/
    └── Localization/
```

### 文件命名规范
- **Views**: `{功能名}View.xaml`
- **ViewModels**: `{功能名}ViewModel.cs`
- **Models**: `{实体名}Model.cs`
- **Services**: `I{服务名}Service.cs` (接口), `{服务名}Service.cs` (实现)
- **Converters**: `{转换类型}Converter.cs`
- **Styles**: `{控件名}Style.xaml`

## MVVM架构标准

### BaseViewModel实现
```csharp
public abstract class BaseViewModel : INotifyPropertyChanged
{
    public event PropertyChangedEventHandler PropertyChanged;
    
    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
    
    protected bool SetProperty<T>(ref T backingStore, T value, [CallerMemberName] string propertyName = "")
    {
        if (EqualityComparer<T>.Default.Equals(backingStore, value))
            return false;
            
        backingStore = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}
```

### Command实现标准
```csharp
public class RelayCommand : ICommand
{
    private readonly Action<object> _execute;
    private readonly Func<object, bool> _canExecute;
    
    public RelayCommand(Action<object> execute, Func<object, bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }
    
    public event EventHandler CanExecuteChanged
    {
        add { CommandManager.RequerySuggested += value; }
        remove { CommandManager.RequerySuggested -= value; }
    }
    
    public bool CanExecute(object parameter) => _canExecute?.Invoke(parameter) ?? true;
    public void Execute(object parameter) => _execute(parameter);
}
```

### ViewModel标准结构
```csharp
public class MainViewModel : BaseViewModel
{
    private string _title;
    private ObservableCollection<ItemModel> _items;
    private ItemModel _selectedItem;
    
    public string Title
    {
        get => _title;
        set => SetProperty(ref _title, value);
    }
    
    public ObservableCollection<ItemModel> Items
    {
        get => _items;
        set => SetProperty(ref _items, value);
    }
    
    public ItemModel SelectedItem
    {
        get => _selectedItem;
        set => SetProperty(ref _selectedItem, value);
    }
    
    public ICommand LoadDataCommand { get; }
    public ICommand SaveCommand { get; }
    public ICommand DeleteCommand { get; }
    
    public MainViewModel()
    {
        LoadDataCommand = new RelayCommand(LoadData);
        SaveCommand = new RelayCommand(Save, CanSave);
        DeleteCommand = new RelayCommand(Delete, CanDelete);
        
        InitializeAsync();
    }
    
    private async void InitializeAsync()
    {
        await LoadDataAsync();
    }
    
    private void LoadData(object parameter) => LoadDataAsync();
    private void Save(object parameter) => SaveAsync();
    private void Delete(object parameter) => DeleteAsync();
    
    private bool CanSave(object parameter) => SelectedItem != null;
    private bool CanDelete(object parameter) => SelectedItem != null;
}
```

## Syncfusion组件使用指南

### 必需的NuGet包
```xml
<PackageReference Include="Syncfusion.SfGrid.WPF" Version="最新版本" />
<PackageReference Include="Syncfusion.SfChart.WPF" Version="最新版本" />
<PackageReference Include="Syncfusion.SfInput.WPF" Version="最新版本" />
<PackageReference Include="Syncfusion.Themes.Windows11Light.WPF" Version="最新版本" />
<PackageReference Include="Syncfusion.Themes.Windows11Dark.WPF" Version="最新版本" />
```

### 主题配置
```csharp
// App.xaml.cs
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // 设置Syncfusion主题
        SfSkinManager.ApplyStylesOnApplication = true;
        SfSkinManager.SetTheme(this, new Theme("Windows11Light"));

        base.OnStartup(e);
    }
}
```

### 常用组件使用标准

#### SfDataGrid使用规范
```xml
<syncfusion:SfDataGrid x:Name="DataGrid"
                       ItemsSource="{Binding Items}"
                       SelectedItem="{Binding SelectedItem}"
                       AutoGenerateColumns="False"
                       AllowEditing="True"
                       AllowSorting="True"
                       AllowFiltering="True"
                       ShowGroupDropArea="True">
    <syncfusion:SfDataGrid.Columns>
        <syncfusion:GridTextColumn MappingName="Name" HeaderText="名称" Width="200"/>
        <syncfusion:GridDateTimeColumn MappingName="CreatedDate" HeaderText="创建日期" Width="150"/>
        <syncfusion:GridNumericColumn MappingName="Value" HeaderText="数值" Width="100"/>
    </syncfusion:SfDataGrid.Columns>
</syncfusion:SfDataGrid>
```

#### SfChart使用规范
```xml
<syncfusion:SfChart Header="数据图表" Margin="10">
    <syncfusion:SfChart.PrimaryAxis>
        <syncfusion:CategoryAxis Header="类别"/>
    </syncfusion:SfChart.PrimaryAxis>
    <syncfusion:SfChart.SecondaryAxis>
        <syncfusion:NumericalAxis Header="数值"/>
    </syncfusion:SfChart.SecondaryAxis>

    <syncfusion:ColumnSeries ItemsSource="{Binding ChartData}"
                            XBindingPath="Category"
                            YBindingPath="Value"
                            Interior="#FF4472C4"/>
</syncfusion:SfChart>
```

#### SfTextInputLayout使用规范
```xml
<syncfusion:SfTextInputLayout Hint="请输入用户名"
                              HelperText="用户名必须唯一"
                              ContainerType="Outlined"
                              FocusedStroke="{StaticResource SystemAccentBrush}">
    <TextBox Text="{Binding UserName, UpdateSourceTrigger=PropertyChanged}"/>
</syncfusion:SfTextInputLayout>
```

## Windows 11主题应用

### 颜色系统
```xml
<!-- Windows11Colors.xaml -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Windows 11 Light Theme Colors -->
    <Color x:Key="SystemAccentColor">#FF0078D4</Color>
    <Color x:Key="SystemAccentColorLight1">#FF40E0FF</Color>
    <Color x:Key="SystemAccentColorLight2">#FF99EBFF</Color>
    <Color x:Key="SystemAccentColorDark1">#FF0063B1</Color>
    <Color x:Key="SystemAccentColorDark2">#FF004E8C</Color>

    <SolidColorBrush x:Key="SystemAccentBrush" Color="{StaticResource SystemAccentColor}"/>
    <SolidColorBrush x:Key="SystemAccentBrushLight1" Color="{StaticResource SystemAccentColorLight1}"/>
    <SolidColorBrush x:Key="SystemAccentBrushLight2" Color="{StaticResource SystemAccentColorLight2}"/>
    <SolidColorBrush x:Key="SystemAccentBrushDark1" Color="{StaticResource SystemAccentColorDark1}"/>
    <SolidColorBrush x:Key="SystemAccentBrushDark2" Color="{StaticResource SystemAccentColorDark2}"/>

    <!-- Background Colors -->
    <Color x:Key="SystemChromeLowColor">#FFF3F3F3</Color>
    <Color x:Key="SystemChromeMediumLowColor">#FFF9F9F9</Color>
    <Color x:Key="SystemChromeMediumColor">#FFE6E6E6</Color>
    <Color x:Key="SystemChromeHighColor">#FFCCCCCC</Color>

    <SolidColorBrush x:Key="SystemChromeLowBrush" Color="{StaticResource SystemChromeLowColor}"/>
    <SolidColorBrush x:Key="SystemChromeMediumLowBrush" Color="{StaticResource SystemChromeMediumLowColor}"/>
    <SolidColorBrush x:Key="SystemChromeMediumBrush" Color="{StaticResource SystemChromeMediumColor}"/>
    <SolidColorBrush x:Key="SystemChromeHighBrush" Color="{StaticResource SystemChromeHighColor}"/>
</ResourceDictionary>
```

### 圆角和阴影标准
```xml
<!-- Windows11Effects.xaml -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Corner Radius -->
    <CornerRadius x:Key="ControlCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="OverlayCornerRadius">8</CornerRadius>

    <!-- Drop Shadows -->
    <DropShadowEffect x:Key="CardShadow"
                      Color="Black"
                      Opacity="0.15"
                      BlurRadius="8"
                      ShadowDepth="2"
                      Direction="270"/>

    <DropShadowEffect x:Key="ElevatedShadow"
                      Color="Black"
                      Opacity="0.25"
                      BlurRadius="16"
                      ShadowDepth="4"
                      Direction="270"/>
</ResourceDictionary>
```

### 标准控件样式

#### Windows 11按钮样式
```xml
<!-- Windows11ButtonStyle.xaml -->
<Style x:Key="Windows11ButtonStyle" TargetType="Button">
    <Setter Property="Background" Value="{StaticResource SystemAccentBrush}"/>
    <Setter Property="Foreground" Value="White"/>
    <Setter Property="BorderThickness" Value="1"/>
    <Setter Property="BorderBrush" Value="{StaticResource SystemAccentBrush}"/>
    <Setter Property="Padding" Value="16,8"/>
    <Setter Property="FontSize" Value="14"/>
    <Setter Property="FontWeight" Value="SemiBold"/>
    <Setter Property="Template">
        <Setter.Value>
            <ControlTemplate TargetType="Button">
                <Border Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource ControlCornerRadius}"
                        Padding="{TemplateBinding Padding}">
                    <ContentPresenter HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                </Border>
                <ControlTemplate.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{StaticResource SystemAccentBrushLight1}"/>
                    </Trigger>
                    <Trigger Property="IsPressed" Value="True">
                        <Setter Property="Background" Value="{StaticResource SystemAccentBrushDark1}"/>
                    </Trigger>
                    <Trigger Property="IsEnabled" Value="False">
                        <Setter Property="Background" Value="{StaticResource SystemChromeMediumBrush}"/>
                        <Setter Property="Foreground" Value="{StaticResource SystemChromeHighBrush}"/>
                    </Trigger>
                </ControlTemplate.Triggers>
            </ControlTemplate>
        </Setter.Value>
    </Setter>
</Style>
```

#### Windows 11卡片样式
```xml
<Style x:Key="Windows11CardStyle" TargetType="Border">
    <Setter Property="Background" Value="White"/>
    <Setter Property="BorderBrush" Value="{StaticResource SystemChromeMediumBrush}"/>
    <Setter Property="BorderThickness" Value="1"/>
    <Setter Property="CornerRadius" Value="{StaticResource OverlayCornerRadius}"/>
    <Setter Property="Effect" Value="{StaticResource CardShadow}"/>
    <Setter Property="Padding" Value="16"/>
    <Setter Property="Margin" Value="8"/>
</Style>
```

## 自定义控件开发标准

### 自定义控件基类
```csharp
public abstract class CustomControlBase : Control
{
    static CustomControlBase()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(CustomControlBase),
            new FrameworkPropertyMetadata(typeof(CustomControlBase)));
    }

    protected override void OnApplyTemplate()
    {
        base.OnApplyTemplate();
        InitializeTemplate();
    }

    protected virtual void InitializeTemplate()
    {
        // 子类重写此方法来初始化模板元素
    }

    protected virtual void OnPropertyChanged(DependencyPropertyChangedEventArgs e)
    {
        // 属性变更处理
    }
}
```

### 依赖属性定义标准
```csharp
public class CustomTextBox : CustomControlBase
{
    // 依赖属性定义
    public static readonly DependencyProperty TextProperty =
        DependencyProperty.Register(
            nameof(Text),
            typeof(string),
            typeof(CustomTextBox),
            new FrameworkPropertyMetadata(
                string.Empty,
                FrameworkPropertyMetadataOptions.BindsTwoWayByDefault,
                OnTextChanged,
                CoerceText));

    public string Text
    {
        get => (string)GetValue(TextProperty);
        set => SetValue(TextProperty, value);
    }

    private static void OnTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is CustomTextBox control)
        {
            control.OnTextChanged((string)e.OldValue, (string)e.NewValue);
        }
    }

    private static object CoerceText(DependencyObject d, object value)
    {
        // 值强制转换逻辑
        return value ?? string.Empty;
    }

    protected virtual void OnTextChanged(string oldValue, string newValue)
    {
        // 文本变更处理逻辑
    }
}
```

### 路由事件定义标准
```csharp
public class CustomButton : CustomControlBase
{
    // 路由事件定义
    public static readonly RoutedEvent ClickEvent =
        EventManager.RegisterRoutedEvent(
            nameof(Click),
            RoutingStrategy.Bubble,
            typeof(RoutedEventHandler),
            typeof(CustomButton));

    public event RoutedEventHandler Click
    {
        add => AddHandler(ClickEvent, value);
        remove => RemoveHandler(ClickEvent, value);
    }

    protected virtual void OnClick()
    {
        RaiseEvent(new RoutedEventArgs(ClickEvent, this));
    }

    protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
    {
        base.OnMouseLeftButtonUp(e);
        OnClick();
    }
}
```

## 数据绑定规范

### 绑定模式标准
```csharp
// ViewModel属性绑定
public string UserName
{
    get => _userName;
    set => SetProperty(ref _userName, value);
}

// 双向绑定示例
<TextBox Text="{Binding UserName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>

// 单向绑定示例
<TextBlock Text="{Binding DisplayText, Mode=OneWay}"/>

// 一次性绑定示例
<TextBlock Text="{Binding StaticText, Mode=OneTime}"/>
```

### 值转换器标准
```csharp
[ValueConversion(typeof(bool), typeof(Visibility))]
public class BoolToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue ? Visibility.Visible : Visibility.Collapsed;
        }
        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Visibility visibility)
        {
            return visibility == Visibility.Visible;
        }
        return false;
    }
}

// 多值转换器示例
public class MultiValueConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        // 多值转换逻辑
        return values.All(v => v is bool b && b);
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
```

### 数据模板标准
```xml
<!-- 列表项数据模板 -->
<DataTemplate x:Key="ItemDataTemplate" DataType="{x:Type local:ItemModel}">
    <Border Style="{StaticResource Windows11CardStyle}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Image Grid.Column="0"
                   Source="{Binding IconPath}"
                   Width="32" Height="32"
                   Margin="0,0,12,0"/>

            <StackPanel Grid.Column="1">
                <TextBlock Text="{Binding Title}"
                          FontWeight="SemiBold"
                          FontSize="14"/>
                <TextBlock Text="{Binding Description}"
                          Foreground="Gray"
                          FontSize="12"/>
            </StackPanel>

            <Button Grid.Column="2"
                    Content="操作"
                    Style="{StaticResource Windows11ButtonStyle}"
                    Command="{Binding DataContext.ActionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    CommandParameter="{Binding}"/>
        </Grid>
    </Border>
</DataTemplate>
```

### 集合绑定标准
```csharp
// ViewModel中的集合属性
private ObservableCollection<ItemModel> _items;
public ObservableCollection<ItemModel> Items
{
    get => _items;
    set => SetProperty(ref _items, value);
}

// 集合视图使用
public ICollectionView ItemsView { get; private set; }

public MainViewModel()
{
    Items = new ObservableCollection<ItemModel>();
    ItemsView = CollectionViewSource.GetDefaultView(Items);
    ItemsView.Filter = FilterItems;
    ItemsView.SortDescriptions.Add(new SortDescription("Name", ListSortDirection.Ascending));
}

private bool FilterItems(object item)
{
    if (item is ItemModel model && !string.IsNullOrEmpty(SearchText))
    {
        return model.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase);
    }
    return true;
}
```

## 动画效果实现

### 标准动画定义
```xml
<!-- 淡入动画 -->
<Storyboard x:Key="FadeInAnimation">
    <DoubleAnimation Storyboard.TargetProperty="Opacity"
                     From="0" To="1"
                     Duration="0:0:0.3"
                     EasingFunction="{StaticResource QuadraticEase}"/>
</Storyboard>

<!-- 滑入动画 -->
<Storyboard x:Key="SlideInAnimation">
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                     From="100" To="0"
                     Duration="0:0:0.4"
                     EasingFunction="{StaticResource QuadraticEase}"/>
</Storyboard>

<!-- 缩放动画 -->
<Storyboard x:Key="ScaleAnimation">
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                     From="0.8" To="1.0"
                     Duration="0:0:0.2"/>
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                     From="0.8" To="1.0"
                     Duration="0:0:0.2"/>
</Storyboard>
```

### 缓动函数定义
```xml
<ResourceDictionary>
    <QuadraticEase x:Key="QuadraticEase" EasingMode="EaseOut"/>
    <CubicEase x:Key="CubicEase" EasingMode="EaseInOut"/>
    <BackEase x:Key="BackEase" EasingMode="EaseOut" Amplitude="0.3"/>
    <ElasticEase x:Key="ElasticEase" EasingMode="EaseOut" Oscillations="1" Springiness="3"/>
</ResourceDictionary>
```

### 触发器动画
```xml
<Style x:Key="AnimatedButtonStyle" TargetType="Button" BasedOn="{StaticResource Windows11ButtonStyle}">
    <Style.Triggers>
        <Trigger Property="IsMouseOver" Value="True">
            <Trigger.EnterActions>
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                       To="1.05" Duration="0:0:0.1"/>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                       To="1.05" Duration="0:0:0.1"/>
                    </Storyboard>
                </BeginStoryboard>
            </Trigger.EnterActions>
            <Trigger.ExitActions>
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                       To="1.0" Duration="0:0:0.1"/>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                       To="1.0" Duration="0:0:0.1"/>
                    </Storyboard>
                </BeginStoryboard>
            </Trigger.ExitActions>
        </Trigger>
    </Style.Triggers>
</Style>
```

## 测试要求

### 单元测试标准
```csharp
[TestClass]
public class MainViewModelTests
{
    private MainViewModel _viewModel;
    private Mock<IDataService> _mockDataService;

    [TestInitialize]
    public void Setup()
    {
        _mockDataService = new Mock<IDataService>();
        _viewModel = new MainViewModel(_mockDataService.Object);
    }

    [TestMethod]
    public void LoadDataCommand_ShouldLoadItems_WhenExecuted()
    {
        // Arrange
        var expectedItems = new List<ItemModel>
        {
            new ItemModel { Id = 1, Name = "Test Item 1" },
            new ItemModel { Id = 2, Name = "Test Item 2" }
        };
        _mockDataService.Setup(s => s.GetItemsAsync()).ReturnsAsync(expectedItems);

        // Act
        _viewModel.LoadDataCommand.Execute(null);

        // Assert
        Assert.AreEqual(2, _viewModel.Items.Count);
        Assert.AreEqual("Test Item 1", _viewModel.Items[0].Name);
        _mockDataService.Verify(s => s.GetItemsAsync(), Times.Once);
    }

    [TestMethod]
    public void SelectedItem_ShouldUpdateCommands_WhenChanged()
    {
        // Arrange
        var item = new ItemModel { Id = 1, Name = "Test Item" };

        // Act
        _viewModel.SelectedItem = item;

        // Assert
        Assert.IsTrue(_viewModel.SaveCommand.CanExecute(null));
        Assert.IsTrue(_viewModel.DeleteCommand.CanExecute(null));
    }
}
```

### 集成测试标准
```csharp
[TestClass]
public class MainViewIntegrationTests
{
    private TestContext _testContext;
    private MainView _mainView;
    private MainViewModel _viewModel;

    [TestInitialize]
    public void Setup()
    {
        _testContext = new TestContext();
        _viewModel = new MainViewModel();
        _mainView = new MainView { DataContext = _viewModel };
    }

    [TestMethod]
    public void DataGrid_ShouldDisplayItems_WhenItemsLoaded()
    {
        // Arrange
        var items = new ObservableCollection<ItemModel>
        {
            new ItemModel { Id = 1, Name = "Test Item 1" },
            new ItemModel { Id = 2, Name = "Test Item 2" }
        };

        // Act
        _viewModel.Items = items;

        // Assert
        var dataGrid = _mainView.FindName("DataGrid") as SfDataGrid;
        Assert.IsNotNull(dataGrid);
        Assert.AreEqual(2, dataGrid.View.Records.Count);
    }
}
```

### UI自动化测试
```csharp
[TestClass]
public class MainViewUITests
{
    private Application _app;
    private Window _mainWindow;

    [TestInitialize]
    public void Setup()
    {
        _app = new Application();
        _mainWindow = new MainWindow();
        _app.MainWindow = _mainWindow;
        _mainWindow.Show();
    }

    [TestCleanup]
    public void Cleanup()
    {
        _mainWindow?.Close();
        _app?.Shutdown();
    }

    [TestMethod]
    public void Button_ShouldTriggerCommand_WhenClicked()
    {
        // 使用UI自动化框架进行测试
        var button = FindElementByAutomationId("LoadDataButton");
        button.Click();

        // 验证结果
        var dataGrid = FindElementByAutomationId("DataGrid");
        Assert.IsTrue(dataGrid.IsEnabled);
    }
}
```

## 代码规范

### 命名规范
- **类名**: PascalCase (例: `MainViewModel`, `UserService`)
- **方法名**: PascalCase (例: `LoadData`, `SaveChanges`)
- **属性名**: PascalCase (例: `UserName`, `IsEnabled`)
- **字段名**: camelCase with underscore prefix (例: `_userName`, `_isLoading`)
- **常量**: UPPER_CASE (例: `MAX_RETRY_COUNT`)
- **事件**: PascalCase with Event suffix (例: `DataLoadedEvent`)

### 注释标准
```csharp
/// <summary>
/// 表示用户数据的视图模型
/// </summary>
/// <remarks>
/// 此类实现了MVVM模式，提供用户数据的绑定和命令处理
/// </remarks>
public class UserViewModel : BaseViewModel
{
    /// <summary>
    /// 获取或设置用户名
    /// </summary>
    /// <value>用户的登录名称</value>
    public string UserName
    {
        get => _userName;
        set => SetProperty(ref _userName, value);
    }

    /// <summary>
    /// 异步加载用户数据
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>表示异步操作的任务</returns>
    /// <exception cref="ArgumentException">当用户ID无效时抛出</exception>
    public async Task LoadUserDataAsync(int userId)
    {
        if (userId <= 0)
            throw new ArgumentException("用户ID必须大于0", nameof(userId));

        try
        {
            // 加载用户数据的实现
            var userData = await _userService.GetUserAsync(userId);
            UserName = userData.Name;
        }
        catch (Exception ex)
        {
            // 记录错误并处理
            _logger.LogError(ex, "加载用户数据失败: {UserId}", userId);
            throw;
        }
    }
}
```

### 文件组织规范
```
// 文件头注释模板
/*
 * 文件名: MainViewModel.cs
 * 作者: AI Assistant
 * 创建日期: 2024-01-01
 * 描述: 主界面视图模型，实现用户数据管理功能
 * 版本: 1.0.0
 */

using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
```

## 性能优化

### 内存管理最佳实践
```csharp
public class OptimizedViewModel : BaseViewModel, IDisposable
{
    private readonly IDataService _dataService;
    private readonly Timer _refreshTimer;
    private bool _disposed = false;

    public OptimizedViewModel(IDataService dataService)
    {
        _dataService = dataService;

        // 使用弱引用避免内存泄漏
        WeakEventManager<IDataService, DataChangedEventArgs>
            .AddHandler(_dataService, nameof(IDataService.DataChanged), OnDataChanged);
    }

    private void OnDataChanged(object sender, DataChangedEventArgs e)
    {
        // 在UI线程上更新数据
        Application.Current.Dispatcher.BeginInvoke(() =>
        {
            RefreshData();
        });
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 清理托管资源
                _refreshTimer?.Dispose();
                WeakEventManager<IDataService, DataChangedEventArgs>
                    .RemoveHandler(_dataService, nameof(IDataService.DataChanged), OnDataChanged);
            }
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
```

### UI虚拟化
```xml
<!-- 大数据集合的虚拟化显示 -->
<syncfusion:SfDataGrid ItemsSource="{Binding LargeDataSet}"
                       VirtualizingPanel.IsVirtualizing="True"
                       VirtualizingPanel.VirtualizationMode="Recycling"
                       EnableDataVirtualization="True"
                       ScrollViewer.CanContentScroll="True">
</syncfusion:SfDataGrid>

<!-- ListBox虚拟化 -->
<ListBox ItemsSource="{Binding Items}"
         VirtualizingPanel.IsVirtualizing="True"
         VirtualizingPanel.VirtualizationMode="Recycling"
         ScrollViewer.CanContentScroll="True">
    <ListBox.ItemsPanel>
        <ItemsPanelTemplate>
            <VirtualizingStackPanel/>
        </ItemsPanelTemplate>
    </ListBox.ItemsPanel>
</ListBox>
```

### 异步操作优化
```csharp
public class AsyncOptimizedViewModel : BaseViewModel
{
    private CancellationTokenSource _cancellationTokenSource;

    public async Task LoadDataAsync()
    {
        // 取消之前的操作
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource = new CancellationTokenSource();

        try
        {
            IsLoading = true;

            // 使用ConfigureAwait(false)避免死锁
            var data = await _dataService
                .GetDataAsync(_cancellationTokenSource.Token)
                .ConfigureAwait(false);

            // 切换回UI线程更新界面
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                Items.Clear();
                foreach (var item in data)
                {
                    Items.Add(item);
                }
            });
        }
        catch (OperationCanceledException)
        {
            // 操作被取消，正常情况
        }
        catch (Exception ex)
        {
            // 处理其他异常
            await ShowErrorAsync(ex.Message);
        }
        finally
        {
            IsLoading = false;
        }
    }
}
```

### 资源优化
```xml
<!-- 使用StaticResource而不是DynamicResource -->
<Button Style="{StaticResource Windows11ButtonStyle}"/>

<!-- 图片资源优化 -->
<Image Source="{Binding ImagePath}">
    <Image.CacheMode>
        <BitmapCache EnableClearType="True" RenderAtScale="1"/>
    </Image.CacheMode>
</Image>

<!-- 减少绑定开销 -->
<TextBlock Text="{Binding DisplayText, Mode=OneTime}"/>
```

## 常见问题解决方案

### 1. 数据绑定不更新
**问题**: ViewModel属性变更后UI不更新
**解决方案**: 确保实现INotifyPropertyChanged并正确调用PropertyChanged事件

### 2. 内存泄漏
**问题**: 应用程序运行时间长后内存持续增长
**解决方案**:
- 正确实现IDisposable接口
- 使用WeakEventManager避免强引用
- 及时取消订阅事件

### 3. UI响应缓慢
**问题**: 大数据量时界面卡顿
**解决方案**:
- 启用UI虚拟化
- 使用异步加载
- 实现数据分页

### 4. Syncfusion主题不生效
**问题**: Syncfusion控件样式显示异常
**解决方案**:
- 确保正确引用主题包
- 在App.xaml.cs中正确设置主题
- 检查资源字典引用顺序

## 开发检查清单

### 功能模块完成检查
- [ ] 实现完整的MVVM架构（Model、View、ViewModel）
- [ ] 使用Syncfusion组件库
- [ ] 应用Windows 11设计语言
- [ ] 包含完整的样式定义
- [ ] 实现数据绑定和命令绑定
- [ ] 添加适当的动画效果
- [ ] 编写单元测试和集成测试
- [ ] 添加完整的代码注释
- [ ] 实现性能优化措施
- [ ] 能够独立编译和运行

### 代码质量检查
- [ ] 遵循命名规范
- [ ] 实现适当的错误处理
- [ ] 使用异步编程模式
- [ ] 实现资源清理
- [ ] 添加日志记录
- [ ] 通过所有测试用例

---

## 总结

本开发规则文档提供了AI助手开发WPF功能模块的完整指导原则。遵循这些规则可以确保：

1. **一致性**: 所有模块都遵循相同的架构和编码标准
2. **可维护性**: 清晰的结构和完整的文档使代码易于维护
3. **可测试性**: 完整的测试覆盖确保代码质量
4. **性能**: 优化措施确保应用程序高效运行
5. **用户体验**: Windows 11设计语言提供现代化的用户界面

每个功能模块都应该作为一个独立的、完整的解决方案来开发，具备所有必要的组件和功能。
