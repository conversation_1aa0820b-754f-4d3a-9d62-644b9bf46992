# WPF AI开发功能模块规则与指导原则

## 🎯 使用场景说明

**本开发规则专门用于指导AI助手在现有WPF项目中开发单个界面或功能模块**

- **目标场景**: 为现有WPF项目添加新的功能模块，而非创建全新项目
- **应用方式**: 当用户需要特定功能时，AI根据这些规则生成可直接集成的完整代码
- **集成要求**: 生成的代码必须能够无缝集成到用户的现有项目结构中

## 目录
1. [概述](#概述)
2. [可测试性要求](#可测试性要求)
3. [代码生成标准](#代码生成标准)
4. [技术栈规范](#技术栈规范)
5. [模块结构规范](#模块结构规范)
6. [MVVM架构标准](#mvvm架构标准)
7. [Syncfusion组件使用指南](#syncfusion组件使用指南)
8. [Windows 11主题应用](#windows-11主题应用)
9. [自定义控件开发标准](#自定义控件开发标准)
10. [数据绑定规范](#数据绑定规范)
11. [动画效果实现](#动画效果实现)
12. [集成指导](#集成指导)
13. [代码规范](#代码规范)
14. [性能优化](#性能优化)

## 概述

本文档定义了AI助手在现有WPF项目中开发单个功能模块时必须遵循的完整规则和标准。

### 核心原则
- **即插即用**: 生成的模块可直接添加到现有项目中，无需修改即可运行
- **自包含性**: 模块不依赖项目中可能不存在的其他组件
- **完整性**: 提供完整的文件内容，包含所有必要的代码和配置
- **标准化**: 遵循MVVM架构、Syncfusion组件库和Windows 11设计语言

## 可测试性要求

### 🔧 代码完整性
- **完整文件内容**: 生成完整的文件内容，而不是代码片段
- **可直接编译**: 所有代码必须包含完整的using语句、命名空间声明
- **无需修改**: 用户可以直接复制粘贴代码到项目中并成功编译

### 📦 依赖管理
- **明确依赖**: 清楚列出所有需要的NuGet包及其版本
- **引用说明**: 提供完整的程序集引用和命名空间导入
- **兼容性**: 确保依赖项与常见的.NET版本兼容

### 🏗️ 自包含性
- **独立运行**: 模块不依赖项目中可能不存在的其他自定义组件
- **基础依赖**: 仅依赖.NET框架、WPF和Syncfusion等标准组件
- **可选集成**: 提供可选的集成点，但不强制要求

## 代码生成标准

### 📁 文件组织要求
每个生成的文件必须包含：
```csharp
// 1. 完整的文件头注释
/*
 * 文件名: [FileName].cs
 * 功能: [功能描述]
 * 创建: AI Assistant
 * 说明: [使用说明和注意事项]
 */

// 2. 完整的using语句
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Input;
using Syncfusion.UI.Xaml.Grid;
// ... 其他必要的引用

// 3. 正确的命名空间
namespace YourProject.ModuleName
{
    // 4. 完整的类实现
}
```

### 🎨 XAML文件标准
```xml
<!-- 1. 完整的XML声明和命名空间 -->
<UserControl x:Class="YourProject.ModuleName.Views.ModuleView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
             xmlns:local="clr-namespace:YourProject.ModuleName.ViewModels">

    <!-- 2. 完整的控件定义 -->
    <!-- 3. 包含所有必要的样式引用 -->
</UserControl>
```

### 📍 文件放置说明
每个生成的文件都必须明确说明放置位置：
```
建议的文件结构：
YourProject/
├── Views/
│   └── [ModuleName]/
│       ├── [ModuleName]View.xaml
│       └── [ModuleName]View.xaml.cs
├── ViewModels/
│   └── [ModuleName]ViewModel.cs
├── Models/
│   └── [ModuleName]Model.cs
└── Styles/
    └── [ModuleName]Styles.xaml
```

## 技术栈规范

### 必需技术栈
- **.NET Framework/Core**: 最新LTS版本
- **WPF**: Windows Presentation Foundation
- **UI组件库**: Syncfusion WPF组件库（优先）
- **设计语言**: Windows 11 Fluent Design
- **架构模式**: MVVM (Model-View-ViewModel)
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **测试框架**: MSTest 或 xUnit

### 可选技术栈
- **自定义控件**: 当Syncfusion无法满足需求时
- **动画库**: Windows.UI.Xaml.Media.Animation
- **数据访问**: Entity Framework Core
- **配置管理**: Microsoft.Extensions.Configuration

## 模块结构规范

### 单个功能模块文件组织
当为现有项目添加新功能模块时，建议按以下结构组织文件：

```
现有项目/
├── Views/
│   └── [ModuleName]/                    # 新模块的视图文件夹
│       ├── [ModuleName]View.xaml        # 主视图
│       ├── [ModuleName]View.xaml.cs     # 主视图代码后台
│       └── UserControls/                # 用户控件（如需要）
│           ├── [Control1].xaml
│           └── [Control1].xaml.cs
├── ViewModels/
│   ├── [ModuleName]ViewModel.cs         # 主视图模型
│   └── [ModuleName]/                    # 模块相关的其他ViewModel
│       └── [SubViewModel].cs
├── Models/
│   └── [ModuleName]/                    # 模块数据模型
│       ├── [Entity]Model.cs
│       └── [DTO]Model.cs
├── Services/
│   └── [ModuleName]/                    # 模块服务
│       ├── I[Service]Service.cs         # 服务接口
│       └── [Service]Service.cs          # 服务实现
├── Styles/
│   └── [ModuleName]/                    # 模块样式
│       ├── [ModuleName]Styles.xaml      # 主样式文件
│       └── Controls/                    # 控件样式
│           └── [Control]Style.xaml
├── Converters/
│   └── [ModuleName]/                    # 模块转换器
│       └── [Type]Converter.cs
└── Resources/
    └── [ModuleName]/                    # 模块资源
        ├── Images/
        └── Icons/
```

### 文件命名规范
- **Views**: `{功能名}View.xaml` (例: `UserManagementView.xaml`)
- **ViewModels**: `{功能名}ViewModel.cs` (例: `UserManagementViewModel.cs`)
- **Models**: `{实体名}Model.cs` (例: `UserModel.cs`, `UserSearchCriteriaModel.cs`)
- **Services**: `I{服务名}Service.cs` (接口), `{服务名}Service.cs` (实现)
- **Converters**: `{转换类型}Converter.cs` (例: `BoolToVisibilityConverter.cs`)
- **Styles**: `{模块名}Styles.xaml` (例: `UserManagementStyles.xaml`)

### 命名空间规范
```csharp
// 主项目命名空间: YourProject
// 模块相关命名空间:
namespace YourProject.Views.UserManagement
namespace YourProject.ViewModels.UserManagement
namespace YourProject.Models.UserManagement
namespace YourProject.Services.UserManagement
namespace YourProject.Converters.UserManagement
```

## MVVM架构标准

### BaseViewModel实现
```csharp
public abstract class BaseViewModel : INotifyPropertyChanged
{
    public event PropertyChangedEventHandler PropertyChanged;
    
    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
    
    protected bool SetProperty<T>(ref T backingStore, T value, [CallerMemberName] string propertyName = "")
    {
        if (EqualityComparer<T>.Default.Equals(backingStore, value))
            return false;
            
        backingStore = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}
```

### Command实现标准
```csharp
public class RelayCommand : ICommand
{
    private readonly Action<object> _execute;
    private readonly Func<object, bool> _canExecute;
    
    public RelayCommand(Action<object> execute, Func<object, bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }
    
    public event EventHandler CanExecuteChanged
    {
        add { CommandManager.RequerySuggested += value; }
        remove { CommandManager.RequerySuggested -= value; }
    }
    
    public bool CanExecute(object parameter) => _canExecute?.Invoke(parameter) ?? true;
    public void Execute(object parameter) => _execute(parameter);
}
```

### ViewModel标准结构
```csharp
public class MainViewModel : BaseViewModel
{
    private string _title;
    private ObservableCollection<ItemModel> _items;
    private ItemModel _selectedItem;
    
    public string Title
    {
        get => _title;
        set => SetProperty(ref _title, value);
    }
    
    public ObservableCollection<ItemModel> Items
    {
        get => _items;
        set => SetProperty(ref _items, value);
    }
    
    public ItemModel SelectedItem
    {
        get => _selectedItem;
        set => SetProperty(ref _selectedItem, value);
    }
    
    public ICommand LoadDataCommand { get; }
    public ICommand SaveCommand { get; }
    public ICommand DeleteCommand { get; }
    
    public MainViewModel()
    {
        LoadDataCommand = new RelayCommand(LoadData);
        SaveCommand = new RelayCommand(Save, CanSave);
        DeleteCommand = new RelayCommand(Delete, CanDelete);
        
        InitializeAsync();
    }
    
    private async void InitializeAsync()
    {
        await LoadDataAsync();
    }
    
    private void LoadData(object parameter) => LoadDataAsync();
    private void Save(object parameter) => SaveAsync();
    private void Delete(object parameter) => DeleteAsync();
    
    private bool CanSave(object parameter) => SelectedItem != null;
    private bool CanDelete(object parameter) => SelectedItem != null;
}
```

## Syncfusion组件使用指南

### 必需的NuGet包
```xml
<PackageReference Include="Syncfusion.SfGrid.WPF" Version="最新版本" />
<PackageReference Include="Syncfusion.SfChart.WPF" Version="最新版本" />
<PackageReference Include="Syncfusion.SfInput.WPF" Version="最新版本" />
<PackageReference Include="Syncfusion.Themes.Windows11Light.WPF" Version="最新版本" />
<PackageReference Include="Syncfusion.Themes.Windows11Dark.WPF" Version="最新版本" />
```

### 主题配置
```csharp
// App.xaml.cs
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // 设置Syncfusion主题
        SfSkinManager.ApplyStylesOnApplication = true;
        SfSkinManager.SetTheme(this, new Theme("Windows11Light"));

        base.OnStartup(e);
    }
}
```

### 常用组件使用标准

#### SfDataGrid使用规范
```xml
<syncfusion:SfDataGrid x:Name="DataGrid"
                       ItemsSource="{Binding Items}"
                       SelectedItem="{Binding SelectedItem}"
                       AutoGenerateColumns="False"
                       AllowEditing="True"
                       AllowSorting="True"
                       AllowFiltering="True"
                       ShowGroupDropArea="True">
    <syncfusion:SfDataGrid.Columns>
        <syncfusion:GridTextColumn MappingName="Name" HeaderText="名称" Width="200"/>
        <syncfusion:GridDateTimeColumn MappingName="CreatedDate" HeaderText="创建日期" Width="150"/>
        <syncfusion:GridNumericColumn MappingName="Value" HeaderText="数值" Width="100"/>
    </syncfusion:SfDataGrid.Columns>
</syncfusion:SfDataGrid>
```

#### SfChart使用规范
```xml
<syncfusion:SfChart Header="数据图表" Margin="10">
    <syncfusion:SfChart.PrimaryAxis>
        <syncfusion:CategoryAxis Header="类别"/>
    </syncfusion:SfChart.PrimaryAxis>
    <syncfusion:SfChart.SecondaryAxis>
        <syncfusion:NumericalAxis Header="数值"/>
    </syncfusion:SfChart.SecondaryAxis>

    <syncfusion:ColumnSeries ItemsSource="{Binding ChartData}"
                            XBindingPath="Category"
                            YBindingPath="Value"
                            Interior="#FF4472C4"/>
</syncfusion:SfChart>
```

#### SfTextInputLayout使用规范
```xml
<syncfusion:SfTextInputLayout Hint="请输入用户名"
                              HelperText="用户名必须唯一"
                              ContainerType="Outlined"
                              FocusedStroke="{StaticResource SystemAccentBrush}">
    <TextBox Text="{Binding UserName, UpdateSourceTrigger=PropertyChanged}"/>
</syncfusion:SfTextInputLayout>
```

## Windows 11主题应用

### 颜色系统
```xml
<!-- Windows11Colors.xaml -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Windows 11 Light Theme Colors -->
    <Color x:Key="SystemAccentColor">#FF0078D4</Color>
    <Color x:Key="SystemAccentColorLight1">#FF40E0FF</Color>
    <Color x:Key="SystemAccentColorLight2">#FF99EBFF</Color>
    <Color x:Key="SystemAccentColorDark1">#FF0063B1</Color>
    <Color x:Key="SystemAccentColorDark2">#FF004E8C</Color>

    <SolidColorBrush x:Key="SystemAccentBrush" Color="{StaticResource SystemAccentColor}"/>
    <SolidColorBrush x:Key="SystemAccentBrushLight1" Color="{StaticResource SystemAccentColorLight1}"/>
    <SolidColorBrush x:Key="SystemAccentBrushLight2" Color="{StaticResource SystemAccentColorLight2}"/>
    <SolidColorBrush x:Key="SystemAccentBrushDark1" Color="{StaticResource SystemAccentColorDark1}"/>
    <SolidColorBrush x:Key="SystemAccentBrushDark2" Color="{StaticResource SystemAccentColorDark2}"/>

    <!-- Background Colors -->
    <Color x:Key="SystemChromeLowColor">#FFF3F3F3</Color>
    <Color x:Key="SystemChromeMediumLowColor">#FFF9F9F9</Color>
    <Color x:Key="SystemChromeMediumColor">#FFE6E6E6</Color>
    <Color x:Key="SystemChromeHighColor">#FFCCCCCC</Color>

    <SolidColorBrush x:Key="SystemChromeLowBrush" Color="{StaticResource SystemChromeLowColor}"/>
    <SolidColorBrush x:Key="SystemChromeMediumLowBrush" Color="{StaticResource SystemChromeMediumLowColor}"/>
    <SolidColorBrush x:Key="SystemChromeMediumBrush" Color="{StaticResource SystemChromeMediumColor}"/>
    <SolidColorBrush x:Key="SystemChromeHighBrush" Color="{StaticResource SystemChromeHighColor}"/>
</ResourceDictionary>
```

### 圆角和阴影标准
```xml
<!-- Windows11Effects.xaml -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Corner Radius -->
    <CornerRadius x:Key="ControlCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="OverlayCornerRadius">8</CornerRadius>

    <!-- Drop Shadows -->
    <DropShadowEffect x:Key="CardShadow"
                      Color="Black"
                      Opacity="0.15"
                      BlurRadius="8"
                      ShadowDepth="2"
                      Direction="270"/>

    <DropShadowEffect x:Key="ElevatedShadow"
                      Color="Black"
                      Opacity="0.25"
                      BlurRadius="16"
                      ShadowDepth="4"
                      Direction="270"/>
</ResourceDictionary>
```

### 标准控件样式

#### Windows 11按钮样式
```xml
<!-- Windows11ButtonStyle.xaml -->
<Style x:Key="Windows11ButtonStyle" TargetType="Button">
    <Setter Property="Background" Value="{StaticResource SystemAccentBrush}"/>
    <Setter Property="Foreground" Value="White"/>
    <Setter Property="BorderThickness" Value="1"/>
    <Setter Property="BorderBrush" Value="{StaticResource SystemAccentBrush}"/>
    <Setter Property="Padding" Value="16,8"/>
    <Setter Property="FontSize" Value="14"/>
    <Setter Property="FontWeight" Value="SemiBold"/>
    <Setter Property="Template">
        <Setter.Value>
            <ControlTemplate TargetType="Button">
                <Border Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource ControlCornerRadius}"
                        Padding="{TemplateBinding Padding}">
                    <ContentPresenter HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                </Border>
                <ControlTemplate.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{StaticResource SystemAccentBrushLight1}"/>
                    </Trigger>
                    <Trigger Property="IsPressed" Value="True">
                        <Setter Property="Background" Value="{StaticResource SystemAccentBrushDark1}"/>
                    </Trigger>
                    <Trigger Property="IsEnabled" Value="False">
                        <Setter Property="Background" Value="{StaticResource SystemChromeMediumBrush}"/>
                        <Setter Property="Foreground" Value="{StaticResource SystemChromeHighBrush}"/>
                    </Trigger>
                </ControlTemplate.Triggers>
            </ControlTemplate>
        </Setter.Value>
    </Setter>
</Style>
```

#### Windows 11卡片样式
```xml
<Style x:Key="Windows11CardStyle" TargetType="Border">
    <Setter Property="Background" Value="White"/>
    <Setter Property="BorderBrush" Value="{StaticResource SystemChromeMediumBrush}"/>
    <Setter Property="BorderThickness" Value="1"/>
    <Setter Property="CornerRadius" Value="{StaticResource OverlayCornerRadius}"/>
    <Setter Property="Effect" Value="{StaticResource CardShadow}"/>
    <Setter Property="Padding" Value="16"/>
    <Setter Property="Margin" Value="8"/>
</Style>
```

## 自定义控件开发标准

### 自定义控件基类
```csharp
public abstract class CustomControlBase : Control
{
    static CustomControlBase()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(CustomControlBase),
            new FrameworkPropertyMetadata(typeof(CustomControlBase)));
    }

    protected override void OnApplyTemplate()
    {
        base.OnApplyTemplate();
        InitializeTemplate();
    }

    protected virtual void InitializeTemplate()
    {
        // 子类重写此方法来初始化模板元素
    }

    protected virtual void OnPropertyChanged(DependencyPropertyChangedEventArgs e)
    {
        // 属性变更处理
    }
}
```

### 依赖属性定义标准
```csharp
public class CustomTextBox : CustomControlBase
{
    // 依赖属性定义
    public static readonly DependencyProperty TextProperty =
        DependencyProperty.Register(
            nameof(Text),
            typeof(string),
            typeof(CustomTextBox),
            new FrameworkPropertyMetadata(
                string.Empty,
                FrameworkPropertyMetadataOptions.BindsTwoWayByDefault,
                OnTextChanged,
                CoerceText));

    public string Text
    {
        get => (string)GetValue(TextProperty);
        set => SetValue(TextProperty, value);
    }

    private static void OnTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is CustomTextBox control)
        {
            control.OnTextChanged((string)e.OldValue, (string)e.NewValue);
        }
    }

    private static object CoerceText(DependencyObject d, object value)
    {
        // 值强制转换逻辑
        return value ?? string.Empty;
    }

    protected virtual void OnTextChanged(string oldValue, string newValue)
    {
        // 文本变更处理逻辑
    }
}
```

### 路由事件定义标准
```csharp
public class CustomButton : CustomControlBase
{
    // 路由事件定义
    public static readonly RoutedEvent ClickEvent =
        EventManager.RegisterRoutedEvent(
            nameof(Click),
            RoutingStrategy.Bubble,
            typeof(RoutedEventHandler),
            typeof(CustomButton));

    public event RoutedEventHandler Click
    {
        add => AddHandler(ClickEvent, value);
        remove => RemoveHandler(ClickEvent, value);
    }

    protected virtual void OnClick()
    {
        RaiseEvent(new RoutedEventArgs(ClickEvent, this));
    }

    protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
    {
        base.OnMouseLeftButtonUp(e);
        OnClick();
    }
}
```

## 数据绑定规范

### 绑定模式标准
```csharp
// ViewModel属性绑定
public string UserName
{
    get => _userName;
    set => SetProperty(ref _userName, value);
}

// 双向绑定示例
<TextBox Text="{Binding UserName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>

// 单向绑定示例
<TextBlock Text="{Binding DisplayText, Mode=OneWay}"/>

// 一次性绑定示例
<TextBlock Text="{Binding StaticText, Mode=OneTime}"/>
```

### 值转换器标准
```csharp
[ValueConversion(typeof(bool), typeof(Visibility))]
public class BoolToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue ? Visibility.Visible : Visibility.Collapsed;
        }
        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Visibility visibility)
        {
            return visibility == Visibility.Visible;
        }
        return false;
    }
}

// 多值转换器示例
public class MultiValueConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        // 多值转换逻辑
        return values.All(v => v is bool b && b);
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
```

### 数据模板标准
```xml
<!-- 列表项数据模板 -->
<DataTemplate x:Key="ItemDataTemplate" DataType="{x:Type local:ItemModel}">
    <Border Style="{StaticResource Windows11CardStyle}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Image Grid.Column="0"
                   Source="{Binding IconPath}"
                   Width="32" Height="32"
                   Margin="0,0,12,0"/>

            <StackPanel Grid.Column="1">
                <TextBlock Text="{Binding Title}"
                          FontWeight="SemiBold"
                          FontSize="14"/>
                <TextBlock Text="{Binding Description}"
                          Foreground="Gray"
                          FontSize="12"/>
            </StackPanel>

            <Button Grid.Column="2"
                    Content="操作"
                    Style="{StaticResource Windows11ButtonStyle}"
                    Command="{Binding DataContext.ActionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    CommandParameter="{Binding}"/>
        </Grid>
    </Border>
</DataTemplate>
```

### 集合绑定标准
```csharp
// ViewModel中的集合属性
private ObservableCollection<ItemModel> _items;
public ObservableCollection<ItemModel> Items
{
    get => _items;
    set => SetProperty(ref _items, value);
}

// 集合视图使用
public ICollectionView ItemsView { get; private set; }

public MainViewModel()
{
    Items = new ObservableCollection<ItemModel>();
    ItemsView = CollectionViewSource.GetDefaultView(Items);
    ItemsView.Filter = FilterItems;
    ItemsView.SortDescriptions.Add(new SortDescription("Name", ListSortDirection.Ascending));
}

private bool FilterItems(object item)
{
    if (item is ItemModel model && !string.IsNullOrEmpty(SearchText))
    {
        return model.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase);
    }
    return true;
}
```

## 动画效果实现

### 标准动画定义
```xml
<!-- 淡入动画 -->
<Storyboard x:Key="FadeInAnimation">
    <DoubleAnimation Storyboard.TargetProperty="Opacity"
                     From="0" To="1"
                     Duration="0:0:0.3"
                     EasingFunction="{StaticResource QuadraticEase}"/>
</Storyboard>

<!-- 滑入动画 -->
<Storyboard x:Key="SlideInAnimation">
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                     From="100" To="0"
                     Duration="0:0:0.4"
                     EasingFunction="{StaticResource QuadraticEase}"/>
</Storyboard>

<!-- 缩放动画 -->
<Storyboard x:Key="ScaleAnimation">
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                     From="0.8" To="1.0"
                     Duration="0:0:0.2"/>
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                     From="0.8" To="1.0"
                     Duration="0:0:0.2"/>
</Storyboard>
```

### 缓动函数定义
```xml
<ResourceDictionary>
    <QuadraticEase x:Key="QuadraticEase" EasingMode="EaseOut"/>
    <CubicEase x:Key="CubicEase" EasingMode="EaseInOut"/>
    <BackEase x:Key="BackEase" EasingMode="EaseOut" Amplitude="0.3"/>
    <ElasticEase x:Key="ElasticEase" EasingMode="EaseOut" Oscillations="1" Springiness="3"/>
</ResourceDictionary>
```

### 触发器动画
```xml
<Style x:Key="AnimatedButtonStyle" TargetType="Button" BasedOn="{StaticResource Windows11ButtonStyle}">
    <Style.Triggers>
        <Trigger Property="IsMouseOver" Value="True">
            <Trigger.EnterActions>
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                       To="1.05" Duration="0:0:0.1"/>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                       To="1.05" Duration="0:0:0.1"/>
                    </Storyboard>
                </BeginStoryboard>
            </Trigger.EnterActions>
            <Trigger.ExitActions>
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                       To="1.0" Duration="0:0:0.1"/>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                       To="1.0" Duration="0:0:0.1"/>
                    </Storyboard>
                </BeginStoryboard>
            </Trigger.ExitActions>
        </Trigger>
    </Style.Triggers>
</Style>
```

## 集成指导

### 🔧 模块集成步骤

#### 1. 添加文件到项目
将生成的文件按照以下结构添加到现有项目中：

```csharp
// 1. 将View文件添加到Views文件夹
// 文件位置: Views/[ModuleName]/[ModuleName]View.xaml
// 文件位置: Views/[ModuleName]/[ModuleName]View.xaml.cs

// 2. 将ViewModel文件添加到ViewModels文件夹
// 文件位置: ViewModels/[ModuleName]ViewModel.cs

// 3. 将Model文件添加到Models文件夹
// 文件位置: Models/[ModuleName]/[Entity]Model.cs

// 4. 将样式文件添加到Styles文件夹
// 文件位置: Styles/[ModuleName]/[ModuleName]Styles.xaml
```

#### 2. 更新项目文件引用
确保项目文件包含所有必要的NuGet包引用：

```xml
<!-- 在.csproj文件中添加以下包引用 -->
<PackageReference Include="Syncfusion.SfGrid.WPF" Version="23.2.7" />
<PackageReference Include="Syncfusion.SfChart.WPF" Version="23.2.7" />
<PackageReference Include="Syncfusion.SfInput.WPF" Version="23.2.7" />
<PackageReference Include="Syncfusion.Themes.Windows11Light.WPF" Version="23.2.7" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
```

#### 3. 配置App.xaml资源字典
在App.xaml中添加模块样式引用：

```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <!-- 现有的资源字典 -->

            <!-- 新模块的样式资源 -->
            <ResourceDictionary Source="Styles/[ModuleName]/[ModuleName]Styles.xaml"/>

            <!-- Syncfusion主题（如果尚未添加） -->
            <ResourceDictionary Source="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
            <ResourceDictionary Source="pack://application:,,,/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBox.xaml" />
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

#### 4. 配置依赖注入（可选）
如果项目使用依赖注入，在启动配置中注册模块服务：

```csharp
// 在App.xaml.cs或Startup.cs中
public void ConfigureServices(IServiceCollection services)
{
    // 注册模块的ViewModel
    services.AddTransient<[ModuleName]ViewModel>();

    // 注册模块的服务（如果有）
    services.AddTransient<I[ModuleName]Service, [ModuleName]Service>();

    // 其他现有服务注册...
}
```

### 🚀 快速集成示例

#### 示例：集成用户管理模块

**步骤1**: 添加文件
```
现有项目/
├── Views/
│   └── UserManagement/
│       ├── UserManagementView.xaml      # 新增
│       └── UserManagementView.xaml.cs   # 新增
├── ViewModels/
│   └── UserManagementViewModel.cs       # 新增
├── Models/
│   └── UserManagement/
│       └── UserModel.cs                 # 新增
└── Styles/
    └── UserManagement/
        └── UserManagementStyles.xaml    # 新增
```

**步骤2**: 在主窗口中使用模块
```xml
<!-- 在MainWindow.xaml或其他容器中 -->
<TabControl>
    <!-- 现有的Tab项 -->

    <!-- 新增的用户管理Tab -->
    <TabItem Header="用户管理">
        <local:UserManagementView/>
    </TabItem>
</TabControl>
```

**步骤3**: 在代码中导航到模块
```csharp
// 在主ViewModel或导航服务中
public void NavigateToUserManagement()
{
    // 方式1: 直接创建View
    var userManagementView = new UserManagementView();
    var userManagementViewModel = new UserManagementViewModel();
    userManagementView.DataContext = userManagementViewModel;

    // 显示在主容器中
    MainContentArea.Content = userManagementView;

    // 方式2: 使用依赖注入（推荐）
    var viewModel = _serviceProvider.GetService<UserManagementViewModel>();
    var view = new UserManagementView { DataContext = viewModel };
    MainContentArea.Content = view;
}
```

### ⚠️ 集成注意事项

#### 命名空间冲突处理
```csharp
// 如果出现命名空间冲突，使用别名
using UserMgmt = YourProject.Views.UserManagement;
using UserModel = YourProject.Models.UserManagement.UserModel;

// 在XAML中使用别名
xmlns:userMgmt="clr-namespace:YourProject.Views.UserManagement"
```

#### 样式冲突处理
```xml
<!-- 使用x:Key避免样式冲突 -->
<Style x:Key="UserManagementButtonStyle" TargetType="Button" BasedOn="{StaticResource Windows11ButtonStyle}">
    <!-- 模块特定的样式设置 -->
</Style>
```

#### 资源冲突处理
```xml
<!-- 在模块样式文件中使用唯一的资源键 -->
<SolidColorBrush x:Key="UserManagement_AccentBrush" Color="#FF0078D4"/>
<CornerRadius x:Key="UserManagement_ControlCornerRadius">4</CornerRadius>
```

### 📋 集成验证清单

- [ ] 所有文件已正确添加到项目中
- [ ] 项目可以成功编译，无编译错误
- [ ] 所有必要的NuGet包已安装
- [ ] 样式资源已正确引用到App.xaml
- [ ] 模块可以正常显示和运行
- [ ] 没有命名空间或资源冲突
- [ ] 依赖注入配置正确（如果使用）
- [ ] 模块功能测试通过

## 测试要求

### 单元测试标准
```csharp
[TestClass]
public class MainViewModelTests
{
    private MainViewModel _viewModel;
    private Mock<IDataService> _mockDataService;

    [TestInitialize]
    public void Setup()
    {
        _mockDataService = new Mock<IDataService>();
        _viewModel = new MainViewModel(_mockDataService.Object);
    }

    [TestMethod]
    public void LoadDataCommand_ShouldLoadItems_WhenExecuted()
    {
        // Arrange
        var expectedItems = new List<ItemModel>
        {
            new ItemModel { Id = 1, Name = "Test Item 1" },
            new ItemModel { Id = 2, Name = "Test Item 2" }
        };
        _mockDataService.Setup(s => s.GetItemsAsync()).ReturnsAsync(expectedItems);

        // Act
        _viewModel.LoadDataCommand.Execute(null);

        // Assert
        Assert.AreEqual(2, _viewModel.Items.Count);
        Assert.AreEqual("Test Item 1", _viewModel.Items[0].Name);
        _mockDataService.Verify(s => s.GetItemsAsync(), Times.Once);
    }

    [TestMethod]
    public void SelectedItem_ShouldUpdateCommands_WhenChanged()
    {
        // Arrange
        var item = new ItemModel { Id = 1, Name = "Test Item" };

        // Act
        _viewModel.SelectedItem = item;

        // Assert
        Assert.IsTrue(_viewModel.SaveCommand.CanExecute(null));
        Assert.IsTrue(_viewModel.DeleteCommand.CanExecute(null));
    }
}
```

### 集成测试标准
```csharp
[TestClass]
public class MainViewIntegrationTests
{
    private TestContext _testContext;
    private MainView _mainView;
    private MainViewModel _viewModel;

    [TestInitialize]
    public void Setup()
    {
        _testContext = new TestContext();
        _viewModel = new MainViewModel();
        _mainView = new MainView { DataContext = _viewModel };
    }

    [TestMethod]
    public void DataGrid_ShouldDisplayItems_WhenItemsLoaded()
    {
        // Arrange
        var items = new ObservableCollection<ItemModel>
        {
            new ItemModel { Id = 1, Name = "Test Item 1" },
            new ItemModel { Id = 2, Name = "Test Item 2" }
        };

        // Act
        _viewModel.Items = items;

        // Assert
        var dataGrid = _mainView.FindName("DataGrid") as SfDataGrid;
        Assert.IsNotNull(dataGrid);
        Assert.AreEqual(2, dataGrid.View.Records.Count);
    }
}
```

### UI自动化测试
```csharp
[TestClass]
public class MainViewUITests
{
    private Application _app;
    private Window _mainWindow;

    [TestInitialize]
    public void Setup()
    {
        _app = new Application();
        _mainWindow = new MainWindow();
        _app.MainWindow = _mainWindow;
        _mainWindow.Show();
    }

    [TestCleanup]
    public void Cleanup()
    {
        _mainWindow?.Close();
        _app?.Shutdown();
    }

    [TestMethod]
    public void Button_ShouldTriggerCommand_WhenClicked()
    {
        // 使用UI自动化框架进行测试
        var button = FindElementByAutomationId("LoadDataButton");
        button.Click();

        // 验证结果
        var dataGrid = FindElementByAutomationId("DataGrid");
        Assert.IsTrue(dataGrid.IsEnabled);
    }
}
```

## 代码规范

### 命名规范
- **类名**: PascalCase (例: `MainViewModel`, `UserService`)
- **方法名**: PascalCase (例: `LoadData`, `SaveChanges`)
- **属性名**: PascalCase (例: `UserName`, `IsEnabled`)
- **字段名**: camelCase with underscore prefix (例: `_userName`, `_isLoading`)
- **常量**: UPPER_CASE (例: `MAX_RETRY_COUNT`)
- **事件**: PascalCase with Event suffix (例: `DataLoadedEvent`)

### 注释标准
```csharp
/// <summary>
/// 表示用户数据的视图模型
/// </summary>
/// <remarks>
/// 此类实现了MVVM模式，提供用户数据的绑定和命令处理
/// </remarks>
public class UserViewModel : BaseViewModel
{
    /// <summary>
    /// 获取或设置用户名
    /// </summary>
    /// <value>用户的登录名称</value>
    public string UserName
    {
        get => _userName;
        set => SetProperty(ref _userName, value);
    }

    /// <summary>
    /// 异步加载用户数据
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>表示异步操作的任务</returns>
    /// <exception cref="ArgumentException">当用户ID无效时抛出</exception>
    public async Task LoadUserDataAsync(int userId)
    {
        if (userId <= 0)
            throw new ArgumentException("用户ID必须大于0", nameof(userId));

        try
        {
            // 加载用户数据的实现
            var userData = await _userService.GetUserAsync(userId);
            UserName = userData.Name;
        }
        catch (Exception ex)
        {
            // 记录错误并处理
            _logger.LogError(ex, "加载用户数据失败: {UserId}", userId);
            throw;
        }
    }
}
```

### 文件组织规范
```
// 文件头注释模板
/*
 * 文件名: MainViewModel.cs
 * 作者: AI Assistant
 * 创建日期: 2024-01-01
 * 描述: 主界面视图模型，实现用户数据管理功能
 * 版本: 1.0.0
 */

using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
```

## 性能优化

### 内存管理最佳实践
```csharp
public class OptimizedViewModel : BaseViewModel, IDisposable
{
    private readonly IDataService _dataService;
    private readonly Timer _refreshTimer;
    private bool _disposed = false;

    public OptimizedViewModel(IDataService dataService)
    {
        _dataService = dataService;

        // 使用弱引用避免内存泄漏
        WeakEventManager<IDataService, DataChangedEventArgs>
            .AddHandler(_dataService, nameof(IDataService.DataChanged), OnDataChanged);
    }

    private void OnDataChanged(object sender, DataChangedEventArgs e)
    {
        // 在UI线程上更新数据
        Application.Current.Dispatcher.BeginInvoke(() =>
        {
            RefreshData();
        });
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 清理托管资源
                _refreshTimer?.Dispose();
                WeakEventManager<IDataService, DataChangedEventArgs>
                    .RemoveHandler(_dataService, nameof(IDataService.DataChanged), OnDataChanged);
            }
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
```

### UI虚拟化
```xml
<!-- 大数据集合的虚拟化显示 -->
<syncfusion:SfDataGrid ItemsSource="{Binding LargeDataSet}"
                       VirtualizingPanel.IsVirtualizing="True"
                       VirtualizingPanel.VirtualizationMode="Recycling"
                       EnableDataVirtualization="True"
                       ScrollViewer.CanContentScroll="True">
</syncfusion:SfDataGrid>

<!-- ListBox虚拟化 -->
<ListBox ItemsSource="{Binding Items}"
         VirtualizingPanel.IsVirtualizing="True"
         VirtualizingPanel.VirtualizationMode="Recycling"
         ScrollViewer.CanContentScroll="True">
    <ListBox.ItemsPanel>
        <ItemsPanelTemplate>
            <VirtualizingStackPanel/>
        </ItemsPanelTemplate>
    </ListBox.ItemsPanel>
</ListBox>
```

### 异步操作优化
```csharp
public class AsyncOptimizedViewModel : BaseViewModel
{
    private CancellationTokenSource _cancellationTokenSource;

    public async Task LoadDataAsync()
    {
        // 取消之前的操作
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource = new CancellationTokenSource();

        try
        {
            IsLoading = true;

            // 使用ConfigureAwait(false)避免死锁
            var data = await _dataService
                .GetDataAsync(_cancellationTokenSource.Token)
                .ConfigureAwait(false);

            // 切换回UI线程更新界面
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                Items.Clear();
                foreach (var item in data)
                {
                    Items.Add(item);
                }
            });
        }
        catch (OperationCanceledException)
        {
            // 操作被取消，正常情况
        }
        catch (Exception ex)
        {
            // 处理其他异常
            await ShowErrorAsync(ex.Message);
        }
        finally
        {
            IsLoading = false;
        }
    }
}
```

### 资源优化
```xml
<!-- 使用StaticResource而不是DynamicResource -->
<Button Style="{StaticResource Windows11ButtonStyle}"/>

<!-- 图片资源优化 -->
<Image Source="{Binding ImagePath}">
    <Image.CacheMode>
        <BitmapCache EnableClearType="True" RenderAtScale="1"/>
    </Image.CacheMode>
</Image>

<!-- 减少绑定开销 -->
<TextBlock Text="{Binding DisplayText, Mode=OneTime}"/>
```

## 常见问题解决方案

### 1. 数据绑定不更新
**问题**: ViewModel属性变更后UI不更新
**解决方案**: 确保实现INotifyPropertyChanged并正确调用PropertyChanged事件

### 2. 内存泄漏
**问题**: 应用程序运行时间长后内存持续增长
**解决方案**:
- 正确实现IDisposable接口
- 使用WeakEventManager避免强引用
- 及时取消订阅事件

### 3. UI响应缓慢
**问题**: 大数据量时界面卡顿
**解决方案**:
- 启用UI虚拟化
- 使用异步加载
- 实现数据分页

### 4. Syncfusion主题不生效
**问题**: Syncfusion控件样式显示异常
**解决方案**:
- 确保正确引用主题包
- 在App.xaml.cs中正确设置主题
- 检查资源字典引用顺序

## 开发检查清单

### 功能模块完成检查
- [ ] 实现完整的MVVM架构（Model、View、ViewModel）
- [ ] 使用Syncfusion组件库
- [ ] 应用Windows 11设计语言
- [ ] 包含完整的样式定义
- [ ] 实现数据绑定和命令绑定
- [ ] 添加适当的动画效果
- [ ] 编写单元测试和集成测试
- [ ] 添加完整的代码注释
- [ ] 实现性能优化措施
- [ ] 能够独立编译和运行

### 代码质量检查
- [ ] 遵循命名规范
- [ ] 实现适当的错误处理
- [ ] 使用异步编程模式
- [ ] 实现资源清理
- [ ] 添加日志记录
- [ ] 通过所有测试用例

### 🎯 AI助手代码生成检查

- [ ] 生成的代码包含完整的文件内容（非代码片段）
- [ ] 所有using语句和命名空间声明完整
- [ ] XAML文件包含正确的命名空间引用
- [ ] 明确说明文件放置位置
- [ ] 列出所有必要的NuGet包依赖
- [ ] 提供集成指导说明
- [ ] 代码可以直接复制粘贴使用

---

## 📋 总结

### 🎯 核心目标

本开发规则文档专门为AI助手在**现有WPF项目中开发单个功能模块**提供完整指导原则。

### ✅ 关键特性

**即插即用性**
- 生成的模块可直接添加到现有项目
- 无需修改即可编译和运行
- 自包含设计，不依赖项目中可能不存在的组件

**完整性保证**
- 提供完整的文件内容，而非代码片段
- 包含所有必要的using语句和命名空间
- 明确的文件组织和放置说明

**标准化实现**
- 严格遵循MVVM架构模式
- 优先使用Syncfusion WPF组件库
- 应用Windows 11设计语言规范

**集成友好**
- 详细的集成步骤和示例
- 冲突处理和解决方案
- 完整的验证检查清单

### 🚀 使用流程

1. **需求分析**: 用户提出具体功能模块需求
2. **代码生成**: AI根据规则生成完整的模块代码
3. **文件集成**: 用户将生成的文件添加到现有项目
4. **配置更新**: 更新必要的引用和资源配置
5. **功能验证**: 确保模块正常工作并集成成功

### 🎯 质量保证

遵循这些规则可以确保：

- **一致性**: 所有模块都遵循相同的架构和编码标准
- **可维护性**: 清晰的结构和完整的文档使代码易于维护
- **可测试性**: 完整的测试覆盖确保代码质量
- **性能**: 优化措施确保应用程序高效运行
- **用户体验**: Windows 11设计语言提供现代化的用户界面
- **集成性**: 模块可以无缝集成到现有项目中

### 💡 最佳实践

每个功能模块都应该：
- 作为独立的、完整的解决方案开发
- 具备所有必要的组件和功能
- 能够在现有项目中即插即用
- 遵循所有质量和性能标准

---

**🔗 相关文档**
- [项目结构模板](./WPF-Module-Template-Structure.md)
- [快速参考指南](./WPF-Quick-Reference-Guide.md)
